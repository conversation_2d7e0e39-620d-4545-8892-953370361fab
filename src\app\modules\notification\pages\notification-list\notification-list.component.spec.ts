import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import NotificationListComponent from './notification-list.component';
import { TranslateModule } from '@ngx-translate/core';

describe('NotificationListComponent', () => {
	let component: NotificationListComponent;
	let fixture: ComponentFixture<NotificationListComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [NotificationListComponent, TranslateModule.forRoot()],
		}).compileComponents();

		fixture = TestBed.createComponent(NotificationListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
