import { TestBed } from '@angular/core/testing';
import { MawbCreateRequestService } from './mawb-create-request.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '@environments/environment';
import { of } from 'rxjs';
import { MawbCreateDto } from '../models/mawb-create.model';
import { HawbCreateDto } from '../../hawb-mgmt/models/hawb-create.model';

const baseUrl = environment.baseApi;

describe('MawbCreateRequestService', () => {
	let service: MawbCreateRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get', 'post', 'patch']);

		TestBed.configureTestingModule({
			providers: [MawbCreateRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(MawbCreateRequestService);
	});

	describe('getHawbDetail', () => {
		it('should call getData with correct endpoint and parameters', () => {
			// Arrange
			const hawbId = 'test-hawb-id-123';
			const mockHawbDetail: HawbCreateDto = {
				id: {
					iri: false,
					namespace: 'test',
					localName: 'test',
					resource: true,
					bnode: false,
					triple: false,
					literal: false,
				},
				orgId: 'org-123',
				waybillPrefix: '123',
				waybillNumber: '********',
				partyList: [],
				accountingInformation: 'test accounting',
				departureLocation: 'NYC',
				arrivalLocation: 'LAX',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				weightValuationIndicator: 'Y',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 750 },
				textualHandlingInstructions: 'Handle with care',
				totalGrossWeight: 100,
				rateClassCode: 'N',
				totalVolumetricWeight: 50,
				rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
				goodsDescriptionForRate: 'Electronics',
				otherChargeList: [],
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'NYC',
				consignorDeclarationSignature: 'John Doe',
				carrierDeclarationSignature: 'Jane Smith',
			};

			// Mock the HTTP GET call
			httpClientSpy.get.and.returnValue(of(mockHawbDetail));

			// Act
			const result = service.getHawbDetail(hawbId);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockHawbDetail);
			});

			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/info`, { params: jasmine.any(Object) });
		});

		it('should handle empty hawbId parameter', () => {
			// Arrange
			const hawbId = '';
			const mockResponse: HawbCreateDto = {} as HawbCreateDto;

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			const result = service.getHawbDetail(hawbId);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});

			expect(httpClientSpy.get).toHaveBeenCalled();
		});

		it('should return Observable<HawbCreateDto>', () => {
			// Arrange
			const hawbId = 'test-id';
			const mockResponse: HawbCreateDto = {} as HawbCreateDto;

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			const result = service.getHawbDetail(hawbId);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});
		});
	});

	describe('createMawb', () => {
		it('should call postData with correct endpoint and data', () => {
			// Arrange
			const mockMawbData: MawbCreateDto = {
				waybillPrefix: '123',
				waybillNumber: '********',
				houseWaybills: ['HWB001', 'HWB002'],
				partyList: [],
				accountingNoteList: [],
				departureLocation: 'NYC',
				arrivalLocation: 'LAX',
				requestedFlight: 'AA123',
				requestedDate: '2024-01-01',
				toFirst: 'First destination',
				toSecond: 'Second destination',
				toThird: 'Third destination',
				byFirstCarrier: 'AA',
				bySecondCarrier: 'DL',
				byThirdCarrier: 'UA',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				carrierChargeCode: 'CC',
				weightValuationIndicator: 'Y',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 750 },
				textualHandlingInstructions: 'Handle with care',
				totalGrossWeight: 100,
				serviceCode: 'SC001',
				rateClassCode: 'N',
				totalVolumetricWeight: 50,
				rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
				goodsDescription: 'Electronics',
				otherChargeList: [],
				destinationCurrencyRate: 1.0,
				destinationCharges: { currencyUnit: 'USD', numericalValue: 100 },
				shippingInfo: 'Standard shipping',
				shippingRefNo: 'REF123',
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'NYC',
				consignorDeclarationSignature: 'John Doe',
				carrierDeclarationSignature: 'Jane Smith',
			};
			const mockResponseId = 'mawb-123';

			httpClientSpy.post.and.returnValue(of(mockResponseId));

			// Act
			const result = service.createMawb(mockMawbData);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponseId);
			});

			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, mockMawbData);
		});

		it('should return Observable<string> with created MAWB ID', () => {
			// Arrange
			const mockMawbData: MawbCreateDto = {} as MawbCreateDto;
			const expectedId = 'new-mawb-id-456';

			httpClientSpy.post.and.returnValue(of(expectedId));

			// Act
			const result = service.createMawb(mockMawbData);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(typeof response).toBe('string');
				expect(response).toEqual(expectedId);
			});
		});

		it('should handle minimal MAWB data', () => {
			// Arrange
			const minimalMawbData: MawbCreateDto = {
				waybillPrefix: '123',
				waybillNumber: '********',
				houseWaybills: [],
				partyList: [],
				accountingNoteList: [],
				departureLocation: 'NYC',
				arrivalLocation: 'LAX',
				requestedFlight: '',
				requestedDate: '',
				toFirst: '',
				toSecond: '',
				toThird: '',
				byFirstCarrier: '',
				bySecondCarrier: '',
				byThirdCarrier: '',
				insuredAmount: { currencyUnit: 'USD', numericalValue: null },
				carrierChargeCode: '',
				weightValuationIndicator: '',
				otherChargesIndicator: '',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: null },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: null },
				textualHandlingInstructions: '',
				totalGrossWeight: null,
				serviceCode: null,
				rateClassCode: null,
				totalVolumetricWeight: null,
				rateCharge: { currencyUnit: 'USD', numericalValue: null },
				goodsDescription: '',
				otherChargeList: [],
				destinationCurrencyRate: null,
				destinationCharges: { currencyUnit: 'USD', numericalValue: null },
				shippingInfo: '',
				shippingRefNo: '',
				carrierDeclarationDate: '',
				carrierDeclarationPlace: '',
				consignorDeclarationSignature: '',
				carrierDeclarationSignature: '',
			};
			const mockResponseId = 'minimal-mawb-id';

			httpClientSpy.post.and.returnValue(of(mockResponseId));

			// Act
			const result = service.createMawb(minimalMawbData);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponseId);
			});

			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, minimalMawbData);
		});
	});

	describe('getHawbDetailBatch', () => {
		it('should call postData with correct endpoint and hawb IDs array', () => {
			// Arrange
			const hawbIds = ['hawb-1', 'hawb-2', 'hawb-3'];
			const mockHawbDetails: HawbCreateDto[] = [
				{
					id: {
						iri: false,
						namespace: 'test1',
						localName: 'test1',
						resource: true,
						bnode: false,
						triple: false,
						literal: false,
					},
					orgId: 'org-1',
					waybillPrefix: '123',
					waybillNumber: '********',
					partyList: [],
					accountingInformation: 'test accounting 1',
					departureLocation: 'NYC',
					arrivalLocation: 'LAX',
					insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
					weightValuationIndicator: 'Y',
					otherChargesIndicator: 'N',
					declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
					declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 750 },
					textualHandlingInstructions: 'Handle with care',
					totalGrossWeight: 100,
					rateClassCode: 'N',
					totalVolumetricWeight: 50,
					rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
					goodsDescriptionForRate: 'Electronics',
					otherChargeList: [],
					carrierDeclarationDate: '2024-01-01',
					carrierDeclarationPlace: 'NYC',
					consignorDeclarationSignature: 'John Doe',
					carrierDeclarationSignature: 'Jane Smith',
				},
				{
					id: {
						iri: false,
						namespace: 'test2',
						localName: 'test2',
						resource: true,
						bnode: false,
						triple: false,
						literal: false,
					},
					orgId: 'org-2',
					waybillPrefix: '456',
					waybillNumber: '********',
					partyList: [],
					accountingInformation: 'test accounting 2',
					departureLocation: 'LAX',
					arrivalLocation: 'NYC',
					insuredAmount: { currencyUnit: 'EUR', numericalValue: 2000 },
					weightValuationIndicator: 'N',
					otherChargesIndicator: 'Y',
					declaredValueForCarriage: { currencyUnit: 'EUR', numericalValue: 1000 },
					declaredValueForCustoms: { currencyUnit: 'EUR', numericalValue: 1500 },
					textualHandlingInstructions: 'Fragile items',
					totalGrossWeight: 200,
					rateClassCode: 'Q',
					totalVolumetricWeight: 100,
					rateCharge: { currencyUnit: 'EUR', numericalValue: 400 },
					goodsDescriptionForRate: 'Machinery',
					otherChargeList: [],
					carrierDeclarationDate: '2024-01-02',
					carrierDeclarationPlace: 'LAX',
					consignorDeclarationSignature: 'Alice Johnson',
					carrierDeclarationSignature: 'Bob Wilson',
				},
			];

			httpClientSpy.post.and.returnValue(of(mockHawbDetails));

			// Act
			const result = service.getHawbDetailBatch(hawbIds);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockHawbDetails);
				expect(response.length).toBe(2);
			});

			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/hawb-management/batchInfo`, hawbIds);
		});

		it('should handle empty hawb IDs array', () => {
			// Arrange
			const hawbIds: string[] = [];
			const mockResponse: HawbCreateDto[] = [];

			httpClientSpy.post.and.returnValue(of(mockResponse));

			// Act
			const result = service.getHawbDetailBatch(hawbIds);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual([]);
				expect(Array.isArray(response)).toBe(true);
			});

			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/hawb-management/batchInfo`, []);
		});

		it('should return Observable<HawbCreateDto[]>', () => {
			// Arrange
			const hawbIds = ['test-hawb-1'];
			const mockResponse: HawbCreateDto[] = [{}] as HawbCreateDto[];

			httpClientSpy.post.and.returnValue(of(mockResponse));

			// Act
			const result = service.getHawbDetailBatch(hawbIds);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(Array.isArray(response)).toBe(true);
			});
		});
	});

	describe('getMawbDetail', () => {
		it('should call getData with correct endpoint and parameters', () => {
			// Arrange
			const mawbId = 'test-mawb-id-456';
			const mockMawbDetail: MawbCreateDto = {
				waybillPrefix: '789',
				waybillNumber: '********',
				houseWaybills: ['HWB003', 'HWB004'],
				partyList: [],
				accountingNoteList: [],
				departureLocation: 'JFK',
				arrivalLocation: 'LHR',
				requestedFlight: 'BA456',
				requestedDate: '2024-02-01',
				toFirst: 'London',
				toSecond: 'Paris',
				toThird: 'Berlin',
				byFirstCarrier: 'BA',
				bySecondCarrier: 'AF',
				byThirdCarrier: 'LH',
				insuredAmount: { currencyUnit: 'GBP', numericalValue: 2000 },
				carrierChargeCode: 'CC2',
				weightValuationIndicator: 'N',
				otherChargesIndicator: 'Y',
				declaredValueForCarriage: { currencyUnit: 'GBP', numericalValue: 1000 },
				declaredValueForCustoms: { currencyUnit: 'GBP', numericalValue: 1500 },
				textualHandlingInstructions: 'Fragile electronics',
				totalGrossWeight: 150,
				serviceCode: 'SC002',
				rateClassCode: 'Q',
				totalVolumetricWeight: 75,
				rateCharge: { currencyUnit: 'GBP', numericalValue: 300 },
				goodsDescription: 'Computer equipment',
				otherChargeList: [],
				destinationCurrencyRate: 1.2,
				destinationCharges: { currencyUnit: 'GBP', numericalValue: 150 },
				shippingInfo: 'Express shipping',
				shippingRefNo: 'REF456',
				carrierDeclarationDate: '2024-02-01',
				carrierDeclarationPlace: 'JFK',
				consignorDeclarationSignature: 'Mary Johnson',
				carrierDeclarationSignature: 'David Brown',
			};

			httpClientSpy.get.and.returnValue(of(mockMawbDetail));

			// Act
			const result = service.getMawbDetail(mawbId);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockMawbDetail);
			});

			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/mawb-management/info`, { params: jasmine.any(Object) });
		});

		it('should handle empty mawbId parameter', () => {
			// Arrange
			const mawbId = '';
			const mockResponse: MawbCreateDto = {} as MawbCreateDto;

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			const result = service.getMawbDetail(mawbId);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});

			expect(httpClientSpy.get).toHaveBeenCalled();
		});

		it('should return Observable<MawbCreateDto>', () => {
			// Arrange
			const mawbId = 'test-mawb-id';
			const mockResponse: MawbCreateDto = {} as MawbCreateDto;

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			const result = service.getMawbDetail(mawbId);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});
		});
	});

	describe('updateMawb', () => {
		it('should call updateDataPatch with correct endpoint and data including mawbId', () => {
			// Arrange
			const mawbId = 'existing-mawb-123';
			const mockMawbData: MawbCreateDto = {
				waybillPrefix: '999',
				waybillNumber: '********',
				houseWaybills: ['HWB005'],
				partyList: [],
				accountingNoteList: [],
				departureLocation: 'ORD',
				arrivalLocation: 'DFW',
				requestedFlight: 'AA789',
				requestedDate: '2024-03-01',
				toFirst: 'Dallas',
				toSecond: '',
				toThird: '',
				byFirstCarrier: 'AA',
				bySecondCarrier: '',
				byThirdCarrier: '',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 3000 },
				carrierChargeCode: 'CC3',
				weightValuationIndicator: 'Y',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 1500 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 2000 },
				textualHandlingInstructions: 'Updated instructions',
				totalGrossWeight: 250,
				serviceCode: 'SC003',
				rateClassCode: 'M',
				totalVolumetricWeight: 125,
				rateCharge: { currencyUnit: 'USD', numericalValue: 500 },
				goodsDescription: 'Updated goods',
				otherChargeList: [],
				destinationCurrencyRate: 1.0,
				destinationCharges: { currencyUnit: 'USD', numericalValue: 200 },
				shippingInfo: 'Updated shipping',
				shippingRefNo: 'REF789',
				carrierDeclarationDate: '2024-03-01',
				carrierDeclarationPlace: 'ORD',
				consignorDeclarationSignature: 'Updated Consignor',
				carrierDeclarationSignature: 'Updated Carrier',
			};
			const mockResponse = { success: true };

			httpClientSpy.patch.and.returnValue(of(mockResponse));

			// Act
			const result = service.updateMawb(mawbId, mockMawbData);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});

			expect(httpClientSpy.patch).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, { ...mockMawbData, id: mawbId });
		});

		it('should handle null mawbId parameter', () => {
			// Arrange
			const mawbId = null as any;
			const mockMawbData: MawbCreateDto = {} as MawbCreateDto;
			const mockResponse = { success: false };

			httpClientSpy.patch.and.returnValue(of(mockResponse));

			// Act
			const result = service.updateMawb(mawbId, mockMawbData);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});

			expect(httpClientSpy.patch).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, { ...mockMawbData, id: null });
		});

		it('should merge mawbId into data object', () => {
			// Arrange
			const mawbId = 'merge-test-id';
			const mockMawbData: MawbCreateDto = {
				waybillPrefix: '555',
				waybillNumber: '55555555',
			} as MawbCreateDto;
			const mockResponse = { updated: true };

			httpClientSpy.patch.and.returnValue(of(mockResponse));

			// Act
			service.updateMawb(mawbId, mockMawbData);

			// Assert
			expect(httpClientSpy.patch).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, {
				...mockMawbData,
				id: mawbId,
			});
		});

		it('should return Observable from updateDataPatch', () => {
			// Arrange
			const mawbId = 'observable-test-id';
			const mockMawbData: MawbCreateDto = {} as MawbCreateDto;
			const mockResponse = { result: 'success' };

			httpClientSpy.patch.and.returnValue(of(mockResponse));

			// Act
			const result = service.updateMawb(mawbId, mockMawbData);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});
		});
	});
});
