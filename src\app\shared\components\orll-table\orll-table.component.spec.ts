import { ChangeDetectorRef, Component, SimpleChange } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { of } from 'rxjs';
import { OrllTableComponent } from './orll-table.component';
import { provideTranslateService, TranslateModule } from '@ngx-translate/core';

const mockData = {
	rows: [
		{ id: 1, name: 'Alice', active: true },
		{ id: 2, name: 'Bob', active: false },
	],
	total: 2,
};
// === Mock Service ===
// eslint-disable-next-line @typescript-eslint/no-unused-vars
class MockGenericTableService<T> {
	getDataPerPage = jasmine.createSpy('getDataPerPage').and.returnValue(of(mockData));
	loadAllData = jasmine.createSpy('loadAllData').and.returnValue(of([]));
}

interface TestItem {
	id: number;
	name: string;
	active: boolean;
}

@Component({
	template: `
		<orll-table [service]="service" [columns]="columns" [param]="param" [hasPagination]="hasPagination" (rowClick)="onRowClick($event)">
		</orll-table>
	`,
	imports: [OrllTableComponent],
})
class TestHostComponent {
	service = new MockGenericTableService<TestItem>();
	hasPagination = true;
	param: any = null;
	columns = [
		{ key: 'id', header: 'ID' },
		{ key: 'name', header: 'Name' },
		{ key: 'active', header: 'Active', transform: (v: boolean) => (v ? 'Yes' : 'No') },
		{
			key: 'requestStatus',
			header: 'common.change.request.status',
			clickCell: () => {
				return false;
			},
		},
	];
	selectedRow: TestItem | null = null;

	onRowClick(row: TestItem) {
		this.selectedRow = row;
	}
}

export interface OrllColumnDef<T> {
	key: string;
	header: string;
	accessor?: (row: T) => any;
	transform?: (value: any) => any;
}

interface Sort {
	active: string;
	direction: 'asc' | 'desc' | '';
}

describe('GenericTableComponent', () => {
	let hostComponent: TestHostComponent;
	let component: OrllTableComponent<TestItem>;
	let fixture: ComponentFixture<TestHostComponent>;
	let service: MockGenericTableService<TestItem>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;

	beforeEach(async () => {
		TestBed.configureTestingModule({
			imports: [
				MatPaginatorModule,
				MatSortModule,
				MatTableModule,
				NoopAnimationsModule,
				OrllTableComponent,
				TranslateModule.forRoot(),
			],
			providers: [
				{
					provide: ChangeDetectorRef,
					useValue: cdrSpy,
				},
				provideTranslateService(),
			],
		}).compileComponents();
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		fixture = TestBed.createComponent(TestHostComponent);
		hostComponent = fixture.componentInstance;
		service = hostComponent.service as MockGenericTableService<TestItem>;
		fixture.detectChanges();
		component = fixture.debugElement.query(By.directive(OrllTableComponent)).componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngAfterViewInit', () => {
		it('should set displayedColumns from columns input', () => {
			expect(component.displayedColumns).toEqual(['id', 'name', 'active', 'requestStatus']);
		});

		it('should assign paginator and sort to dataSource', () => {
			expect(component.dataSource.paginator).toBe(component.paginator);
			expect(component.dataSource.sort).toBe(component.sort);
		});

		it('should call loadData on init', () => {
			expect(service.getDataPerPage).toHaveBeenCalled();
		});
	});

	describe('ngOnChanges', () => {
		it('should update param when id or type changes', () => {
			const param = { allLoad: true };
			component.param = param;
			component.ngOnChanges({ param: new SimpleChange(null, param, true) });

			expect(component.param).toEqual({ allLoad: true });
		});

		it('should not update param if no relevant changes', () => {
			const oldParam = component.param;
			component.ngOnChanges({});
			expect(component.param).toBe(oldParam);
		});
	});

	describe('columns', () => {
		it('should initialize columns with correct keys and headers', () => {
			const expectedKeys = ['id', 'name', 'active', 'requestStatus'];
			component.columns.forEach((col, index) => {
				expect(col.key).toBe(expectedKeys[index]);
			});
		});

		it('should have a clickCell handler in requestStatus column', () => {
			const statusCol = component.columns.find((c) => c.key === 'requestStatus');
			expect(statusCol).toBeDefined();
			expect(statusCol?.clickCell).toEqual(jasmine.any(Function));
		});
	});

	describe('onPageChange', () => {
		it('should update pageIndex and pageSize and reload data', () => {
			const spy = spyOn(component, 'loadData');
			const event = { pageIndex: 1, pageSize: 20, length: 100 };
			component.onPageChange(event);

			expect(component.pageIndex).toBe(1);
			expect(component.pageSize).toBe(20);
			expect(spy).toHaveBeenCalled();
		});
	});

	describe('onSortChange', () => {
		it('should update sort field and direction and reload data', () => {
			const spy = spyOn(component, 'loadData');
			const sortEvent = { active: 'name', direction: 'desc' } as Sort;
			component.onSortChange(sortEvent);

			expect(component.sortField).toBe('name');
			expect(component.sortDirection).toBe('desc');
			expect(component.pageIndex).toBe(0);
			expect(spy).toHaveBeenCalled();
		});

		it('should reset sort when direction is empty', () => {
			component.sortField = 'name';
			component.sortDirection = 'desc';
			const sortEvent = { active: 'name', direction: '' } as Sort;
			component.onSortChange(sortEvent);

			expect(component.sortField).toBe('');
			expect(component.sortDirection).toBe('asc');
		});
	});

	describe('onRowClicked', () => {
		it('should emit rowClick event', () => {
			const row = mockData.rows[0];
			component.rowClick.subscribe((emittedRow) => {
				expect(emittedRow).toBe(row);
			});
			component.onRowClicked(row);
		});
	});

	describe('getCellValue', () => {
		beforeEach(() => {
			component.dataSource.data = mockData.rows;
		});

		it('should get value by key if no accessor', () => {
			const col = { key: 'name' } as OrllColumnDef<TestItem>;
			const value = component.getCellValue(col, mockData.rows[0]);
			expect(value).toBe('Alice');
		});

		it('should use accessor if provided', () => {
			const col = {
				key: 'name',
				accessor: (row: TestItem) => `User: ${row.name}`,
			} as OrllColumnDef<TestItem>;
			const value = component.getCellValue(col, mockData.rows[0]);
			expect(value).toBe('User: Alice');
		});

		it('should apply transform function', () => {
			const col = {
				key: 'active',
				transform: (v: boolean) => (v ? 'Enabled' : 'Disabled'),
			} as OrllColumnDef<TestItem>;
			const value = component.getCellValue(col, mockData.rows[1]);
			expect(value).toBe('Disabled');
		});

		it('should apply transform after accessor', () => {
			const col = {
				key: 'active',
				accessor: (row: TestItem) => !row.active,
				transform: (v: boolean) => (v ? 'Inverted: ON' : 'Inverted: OFF'),
			} as OrllColumnDef<TestItem>;
			const value = component.getCellValue(col, mockData.rows[0]); // active = true
			expect(value).toBe('Inverted: OFF');
		});
	});

	describe('Template Integration', () => {
		it('should render table with correct columns', () => {
			component.hasPagination = false;
			service.getDataPerPage.and.returnValue(of(mockData));
			fixture.detectChanges();

			const headers = fixture.nativeElement.querySelectorAll('th');
			expect(headers.length).toBeGreaterThan(0);
			expect(headers[0].textContent).toContain('ID');
			expect(headers[1].textContent).toContain('Name');
		});

		it('should render table rows', () => {
			service.getDataPerPage.and.returnValue(of(mockData));
			fixture.detectChanges();

			const rows = fixture.nativeElement.querySelectorAll('tbody tr');
			expect(rows.length).toBe(2);
		});
	});
});
