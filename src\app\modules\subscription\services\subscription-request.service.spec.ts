import { TestBed } from '@angular/core/testing';
import { SubscriptionRequestService } from './subscription-request.service';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { environment } from '@environments/environment';
import { SubscriptionListObj, SubscriptionRequest } from '../models/subscription.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { LogisticObjType } from '@shared/models/share-type.model';

const baseUrl = environment.baseApi;

describe('SubscriptionService', () => {
	let service: SubscriptionRequestService;
	let httpMock: HttpTestingController;

	const mockAllData: SubscriptionListObj[] = [
		{
			topic: '111',
			topicType: 'Log',
			createDate: '2025-08-01',
			approvedDate: '2025-08-01',
			subscriptionType: '1222',
			status: 'Approved',
			isRequestedBy: 'company a',
			isRequestedAt: '2025-08-01',
			subscriberOrgName: '111',
			orgId: '',
			id: '',
		},
	];

	const mockPagedRequest: SubscriptionRequest = {
		subscriberId: '111',
		fromTab: false,
	};
	const mockPagedResponse: PaginationResponse<SubscriptionListObj> = {
		total: 25,
		pageNum: 1,
		rows: [
			{
				topic: '111',
				topicType: 'Log',
				createDate: '2025-08-01',
				approvedDate: '2025-08-01',
				subscriptionType: '1222',
				status: 'Approved',
				isRequestedBy: 'company a',
				isRequestedAt: '2025-08-01',
				subscriberOrgName: '111',
				orgId: '',
				id: '',
			},
			{
				topic: '222',
				topicType: 'Log',
				createDate: '2025-08-01',
				approvedDate: '2025-08-01',
				subscriptionType: '1222',
				status: 'Approved',
				isRequestedBy: 'company a',
				isRequestedAt: '2025-08-01',
				subscriberOrgName: '111',
				orgId: '',
				id: '',
			},
		],
	};

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [],
			providers: [SubscriptionRequestService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(SubscriptionRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should send POST request and return SubscriptionListObj[]', () => {
		const param = { filter: 'all' };

		service.loadAllData(param).subscribe((data) => {
			expect(data).toEqual(mockAllData);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(param);
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(mockAllData);
	});

	it('should send POST request and return PaginationResponse<SubListObj>', () => {
		service.getDataPerPage(mockPagedRequest).subscribe((response) => {
			expect(response).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(mockPagedRequest);

		req.flush(mockPagedResponse);
	});

	it('should handle empty response for loadAllData', () => {
		service.loadAllData({}).subscribe((data) => {
			expect(data.length).toBe(0);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		req.flush([]);
	});

	it('should handle pagination with total count', () => {
		service
			.getDataPerPage({
				subscriberId: '111',
				fromTab: false,
			})
			.subscribe((res) => {
				expect(res.total).toBe(25);
				expect(res.pageNum).toBe(1);
			});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		req.flush(mockPagedResponse);
	});

	it('should return [] if fromTab is true - topic is empty', () => {
		service
			.getDataPerPage({
				subscriptionType: LogisticObjType.HAWB,
				fromTab: true,
				topic: '',
			})
			.subscribe((res) => {
				expect(res.total).toBe(0);
				expect(res.pageNum).toBe(1);
			});

		httpMock.expectNone(`${baseUrl}/user-subscriptions-request/list`);
	});

	it('should fail gracefully on 500 error', () => {
		const errorMsg = 'Internal Server Error';

		service.loadAllData({}).subscribe({
			next: () => fail('should have failed'),
			error: (error) => {
				expect(error.status).toBe(500);
				expect(error.error.message).toContain(errorMsg);
			},
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions-request/list`);
		req.flush({ message: errorMsg }, { status: 500, statusText: 'Internal Server Error' });
	});
});
