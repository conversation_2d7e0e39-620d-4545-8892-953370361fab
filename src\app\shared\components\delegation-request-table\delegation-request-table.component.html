<div class="orll-delegation-request-table__container">
	@if (isCentralMenu) {
		<div class="orll-delegation-request-table__create">
			<button mat-flat-button color="primary" (click)="createDelegationRequest()">
				<mat-icon>add</mat-icon>
				{{ 'common.dialog.delegation.request.create' | translate }}
			</button>
		</div>
	}
	<div class="common-scroll-table-container" (scroll)="onTableScroll($event)">
		<table
			mat-table
			[dataSource]="dataSource"
			[trackBy]="trackByLoId"
			matSort
			(matSortChange)="onSortChange($event)"
			aria-label="delegation request table"
			class="common-scroll-table">
			<ng-container matColumnDef="hasDescription">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="orll-delegation-request-table__description">
					{{ 'common.dialog.delegation.request.description' | translate }}
				</th>
				<td mat-cell *matCellDef="let record" class="orll-delegation-request-table__description">
					{{ record.hasDescription }}
				</td>
			</ng-container>

			<ng-container matColumnDef="isRequestedFor">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'common.dialog.delegation.request.for' | translate }}
				</th>
				<td mat-cell *matCellDef="let record" class="orll-delegation-request-table__cell">{{ record.isRequestedFor }}</td>
			</ng-container>

			<ng-container matColumnDef="isRequestedBy">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'common.dialog.delegation.request.by' | translate }}
				</th>
				<td mat-cell *matCellDef="let record" class="orll-delegation-request-table__cell">{{ record.isRequestedBy }}</td>
			</ng-container>

			<ng-container matColumnDef="isRequestedAt">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'common.dialog.delegation.request.at' | translate }}
				</th>
				<td mat-cell *matCellDef="let record" class="orll-delegation-request-table__cell">{{ record.isRequestedAt }}</td>
			</ng-container>

			<ng-container matColumnDef="requestStatus">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'common.dialog.delegation.request.status' | translate }}
				</th>
				<td mat-cell *matCellDef="let record" class="orll-delegation-request-table__cell">
					<a class="request-status__link" (click)="openDelegationRequestDetail(record)">
						{{ record.requestStatus.substring(8) }}
					</a>
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
			<tr mat-row *matRowDef="let record; columns: displayedColumns" class="orll-delegation-request-table__row"></tr>
		</table>
	</div>

	@if (!enableInfiniteScroll) {
		<mat-paginator [pageSizeOptions]="tablePageSizes" [length]="totalRecords" (page)="pagination.emit($event)"></mat-paginator>
	}

	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
