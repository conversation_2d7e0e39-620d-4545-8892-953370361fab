.orll-sli-create-page {
	margin-top: 40px;

	.iata-box {
		overflow-y: auto;
	}

	.tab-content-container {
		padding: 20px;
	}
	.width-100 {
		width: 100%;
	}

	.width-10 {
		width: 10%;
	}

	.width-13 {
		width: 13%;
	}

	.width-5 {
		width: 5%;
	}

	.margin-auto {
		margin: auto;
	}

	.margin-b-20 {
		margin-bottom: 20px;
	}

	.margin-t-20 {
		margin-top: 20px;
	}

	.declared-value {
		margin-left: 25px;
	}

	.total-gross-weight {
		margin-right: 12px;
	}

	.total-dimensions {
		margin-left: -2px;
	}

	.col-1 {
		flex: 0 0 7% !important;
		max-width: 7% !important;
	}

	.unit {
		margin-right: 5px;
		font-size: 14px;
		color: var(--iata-black);
	}

	.incoterms {
		margin-top: -61px;
	}

	.autocomplete-arrow {
		padding: 0 5px;
	}

	::ng-deep .mat-mdc-form-field-icon-suffix {
		padding: 0;
	}
	.row-gap {
		gap: 20px;
	}

	.row {
		display: flex;
		margin-bottom: 10px;
		justify-content: space-between;

		.iata-shipper-box {
			position: relative;
			margin-bottom: 20px;
			border-radius: 8px;
			padding: 20px;
			flex: 1;
			border: 1px solid var(--iata-grey-200);
		}
		.fill {
			flex: 1 1 auto;
		}
	}
	.orll-sli-create-page__footer {
		display: flex;
		justify-content: flex-end;
		gap: 10px;
	}
	&__tab {
		padding-top: 20px;
	}
	::ng-deep .mat-expansion-panel-header-description {
		flex-grow: 0 !important;
	}
}
