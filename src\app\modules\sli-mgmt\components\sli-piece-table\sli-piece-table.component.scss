.orll-sli-piece-table {
	&__container {
		min-height: 200px;
	}

	&__tree_col {
		min-height: 56px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0px !important;
		padding-left: 16px !important;
	}
	&__tree_col_icon {
		margin-top: 5px;
		font-size: 16px;
	}
	&__tree_col_head {
		width: 10rem;
	}
	&__description {
		padding: 0px;
	}

	&__create {
		display: flex;
		align-items: center;
	}
	&__table_info {
		margin: 10px 0 0 25px;
		margin-right: auto;
		display: flex;
		gap: 1rem;
		align-items: center;

		.label {
			color: var(--iata-grey-300);
		}
	}

	&__delete-button {
		margin: 0 20px !important;
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);

		&.mat-mdc-button-disabled {
			background-color: var(--iata-blue-grey-50);
			color: var(--iata-grey-300) !important;
			border: 1px solid var(--iata-grey-300);
		}
	}

	&__hawb-tab {
		display: flex;
		margin-bottom: 20px;
		.bold {
			font-size: 18px;
			font-weight: 600;
		}
	}

	.latest-status__link {
		color: var(--iata-blue-primary);
		text-decoration: underline;
		cursor: pointer;
	}
}
