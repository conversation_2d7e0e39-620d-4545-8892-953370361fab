import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceTableComponent } from './sli-piece-table.component';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PageEvent, MatPaginatorIntl } from '@angular/material/paginator';
import { SimpleChange } from '@angular/core';
import { PieceList } from '../../models/piece/piece-list.model';
import { MatDialog } from '@angular/material/dialog';
import { of, Subject } from 'rxjs';
import { Sort } from '@angular/material/sort';
import { PieceType } from '../../models/piece/piece-type.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';

describe('SliPieceTableComponent', () => {
	let component: SliPieceTableComponent;
	let fixture: ComponentFixture<SliPieceTableComponent>;
	let mockPieces: PieceList[];
	let selectedItems: PieceList[];
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let mockPaginatorIntl: jasmine.SpyObj<MatPaginatorIntl>;

	beforeEach(async () => {
		selectedItems = [];
		const mockSelection = {
			clear: jasmine.createSpy('clear').and.callFake(() => {
				selectedItems.length = 0;
				return mockSelection;
			}),
			select: jasmine.createSpy('select').and.callFake((...pieces: PieceList[]) => {
				pieces.forEach((piece) => {
					if (!selectedItems.includes(piece)) {
						selectedItems.push(piece);
					}
				});
				return mockSelection;
			}),
			get selected() {
				return selectedItems;
			},
		} as any as SelectionModel<PieceList>;

		mockPieces = [
			{
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			} as PieceList,
			{
				type: 'Piece',
				pieceId: 'TEST456',
				productDescription: '456',
				packagingType: '789',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			} as PieceList,
		];

		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);

		// Create mock TranslateService with onLangChange Subject
		const onLangChangeSubject = new Subject();
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant'], {
			onLangChange: onLangChangeSubject.asObservable(),
		});
		mockTranslateService.instant.and.returnValue('Delete confirmation message');

		// Create mock SliCreateRequestService
		mockSliCreateRequestService = jasmine.createSpyObj('SliCreateRequestService', ['deletePiece', 'getTotalPieceQuantity']);
		mockSliCreateRequestService.deletePiece.and.returnValue(of('success'));
		mockSliCreateRequestService.getTotalPieceQuantity.and.returnValue(of({ totalQuantity: 5, totalSlac: 5 }));

		// Create mock MatPaginatorIntl
		mockPaginatorIntl = jasmine.createSpyObj('MatPaginatorIntl', [], {
			changes: new Subject(),
			itemsPerPageLabel: 'Items per page:',
			nextPageLabel: 'Next page',
			previousPageLabel: 'Previous page',
			firstPageLabel: 'First page',
			lastPageLabel: 'Last page',
		});

		await TestBed.configureTestingModule({
			imports: [SliPieceTableComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: TranslateService, useValue: mockTranslateService },
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				{ provide: MatPaginatorIntl, useValue: mockPaginatorIntl },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceTableComponent);
		component = fixture.componentInstance;

		// Mock dependencies
		component.selection = mockSelection;
		component.dataSource = new MatTableDataSource<PieceList>(mockPieces);
		component.pageParams = { pageNum: 1, pageSize: 10 };
		component.totalRecords = 20;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should modify displayedColumns when hawbId is provided', () => {
			component.hawbId = 'HAWB123';
			spyOn(component.displayedColumns, 'shift').and.callThrough();
			spyOn(component.displayedColumns, 'pop').and.callThrough();
			spyOn(component.displayedColumns, 'push').and.callThrough();

			component.ngOnInit();

			expect(component.displayedColumns.shift).toHaveBeenCalled();
			expect(component.displayedColumns.pop).toHaveBeenCalled();
			expect(component.displayedColumns.push).toHaveBeenCalledWith('latestStatus');
		});

		it('should not modify displayedColumns when hawbId is empty', () => {
			component.hawbId = '';
			spyOn(component.displayedColumns, 'shift');
			spyOn(component.displayedColumns, 'pop');
			spyOn(component.displayedColumns, 'push');

			component.ngOnInit();

			expect(component.displayedColumns.shift).not.toHaveBeenCalled();
			expect(component.displayedColumns.pop).not.toHaveBeenCalled();
			expect(component.displayedColumns.push).not.toHaveBeenCalled();
		});

		it('should not modify displayedColumns when hawbId is null', () => {
			component.hawbId = null as any;
			spyOn(component.displayedColumns, 'shift');
			spyOn(component.displayedColumns, 'pop');
			spyOn(component.displayedColumns, 'push');

			component.ngOnInit();

			expect(component.displayedColumns.shift).not.toHaveBeenCalled();
			expect(component.displayedColumns.pop).not.toHaveBeenCalled();
			expect(component.displayedColumns.push).not.toHaveBeenCalled();
		});
	});

	describe('ngOnChanges', () => {
		it('should update dataSource and not clear selection when records change', () => {
			// Set up the component with initial empty data
			component.dataSource = new MatTableDataSource<PieceList>([]);

			// Create a changes object with the records property
			const changes = {
				records: new SimpleChange(null, mockPieces, true),
			};

			// Set the records input property
			component.records = mockPieces;

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data contains the mock pieces
			expect(component.dataSource.data.length).toEqual(2);
			expect(component.dataSource.data[0]).toEqual(mockPieces[0]);
			expect(component.dataSource.data[1]).toEqual(mockPieces[1]);

			// Verify that selection was cleared
			expect(component.selection.clear).not.toHaveBeenCalled();
		});

		it('should update dataSource and  clear selection when records change', () => {
			// Set up the component with initial empty data
			component.dataSource = new MatTableDataSource<PieceList>([]);

			// Create a changes object with the records property
			const changes = {
				records: new SimpleChange(null, mockPieces, true),
				totalSlac: new SimpleChange(null, 30, true),
			};

			// Set the records input property
			component.records = mockPieces;

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data contains the mock pieces
			expect(component.dataSource.data.length).toEqual(2);
			expect(component.dataSource.data[0]).toEqual(mockPieces[0]);
			expect(component.dataSource.data[1]).toEqual(mockPieces[1]);

			// Verify that selection was cleared
			expect(component.selection.clear).toHaveBeenCalled();
		});

		it('should handle empty records array', () => {
			const changes = {
				records: new SimpleChange(mockPieces, [], false),
			};

			component.records = [];
			component.ngOnChanges(changes);

			expect(component.totalSlac).toBe(0);
			expect(component.totalQuantity).toBe(0);
			expect(component.latestStatus).toBe(''); // Empty string when no records
		});

		it('should not update dataSource when records do not change', () => {
			// Set up the component with initial data
			const initialData = [...mockPieces];
			component.dataSource = new MatTableDataSource<PieceList>(initialData);

			// Create a changes object without the records property
			const changes = {
				totalRecords: new SimpleChange(0, 20, true),
			};

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data remains unchanged
			expect(component.dataSource.data).toEqual(initialData);
			expect(component.selection.clear).toHaveBeenCalled();
		});
	});

	describe('onTableScroll', () => {
		it('should emit loadMoreData when conditions are met', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).toHaveBeenCalled();
		});

		it('should not emit loadMoreData when enableInfiniteScroll is false', () => {
			component.enableInfiniteScroll = false;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should not emit loadMoreData when dataLoading is true', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = true;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should not emit loadMoreData when hasMoreData is false', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = false;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should not emit loadMoreData when not at bottom', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 50, // Not at bottom
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should handle edge case when scrollHeight equals scrollTop + clientHeight', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100, // scrollTop + clientHeight = scrollHeight
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).toHaveBeenCalled();
		});
	});

	describe('onSortChange', () => {
		it('should update currentSort and emit sortChange event', () => {
			spyOn(component.sortChange, 'emit');
			spyOn(component.pagination, 'emit');

			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.currentSort).toEqual(sort);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sort);
			expect(component.pagination.emit).toHaveBeenCalled();
		});
	});

	describe('emitPaginationWithSort', () => {
		it('should emit pagination event with current sort parameters when no event provided', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'productDescription', direction: 'asc' };

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 0,
				pageSize: 10,
				length: 20,
				sortField: 'productDescription',
				sortDirection: 'asc',
			});
		});

		it('should emit pagination event with provided event and current sort parameters', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'grossWeight', direction: 'desc' };

			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
			};

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				...pageEvent,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			});
		});
	});

	describe('isAllSelected', () => {
		it('should return false when no rows are selected', () => {
			selectedItems.length = 0;
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when some rows are selected', () => {
			selectedItems.length = 0;
			component.selection.select(mockPieces[0]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return true when all rows are selected', () => {
			selectedItems.length = 0;
			component.selection.select(...mockPieces);
			expect(component.isAllSelected()).toBe(true);
		});

		it('should return false when dataSource is empty', () => {
			selectedItems.length = 0;
			component.dataSource = new MatTableDataSource<PieceList>([]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when dataSource is empty but selection is not', () => {
			selectedItems.length = 0;
			component.dataSource = new MatTableDataSource<PieceList>([]);
			component.selection.select(mockPieces[0]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when more items are selected than exist in dataSource', () => {
			selectedItems.length = 0;
			const extraPiece = {
				type: 'Piece',
				pieceId: 'TEST789',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			} as PieceList;
			component.selection.select(...mockPieces, extraPiece);
			expect(component.isAllSelected()).toBe(false);
		});
	});

	describe('toggleAllRows', () => {
		it('should clear selection when all rows are selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			component.selection.select(...mockPieces);
			const clearSpy = component.selection.clear;

			component.toggleAllRows();

			expect(clearSpy).toHaveBeenCalled();
		});

		it('should select all when none selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			const selectSpy = component.selection.select;

			component.toggleAllRows();

			expect(selectSpy).toHaveBeenCalled();
			expect(selectedItems.length).toBe(mockPieces.length);
		});
	});

	describe('trackByPieceId', () => {
		it('should return the pieceId of the given piece', () => {
			const mockPiece: PieceList = {
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			};

			expect(component.trackByPieceId(mockPiece)).toBe('TEST123');
		});
	});

	describe('addPiece', () => {
		it('should open dialog and emit saveRequest when result is returned', () => {
			const dialogRefMock = {
				afterClosed: () => of('general'),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.saveRequest, 'emit');

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.saveRequest.emit).toHaveBeenCalledWith({ pieceType: 'general' });
		});

		it('should not emit saveRequest when dialog is closed without result', () => {
			const dialogRefMock = {
				afterClosed: () => of(undefined),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.saveRequest, 'emit');

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.saveRequest.emit).not.toHaveBeenCalled();
		});
	});

	describe('editPiece', () => {
		it('should stop event propagation and emit saveRequest with general pieceType', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: PieceType.GENERAL };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(eventMock.stopPropagation).toHaveBeenCalled();
			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: 'general',
				pieceId: piece.pieceId,
			});
		});

		it('should emit saveRequest with dg pieceType for dangerous goods', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: PieceType.DANGEROUS_GOODS };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: 'dg',
				pieceId: piece.pieceId,
			});
		});

		it('should emit saveRequest with la pieceType for live animals', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: PieceType.LIVE_ANIMALS };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: 'la',
				pieceId: piece.pieceId,
			});
		});

		it('should emit saveRequest with empty pieceType for unknown type', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: 'Unknown' as any };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: '',
				pieceId: piece.pieceId,
			});
		});
	});

	describe('pagination', () => {
		it('should emit pagination event when page changes', () => {
			spyOn(component.pagination, 'emit');
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 10,
				length: 100,
			};

			component.pagination.emit(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
		});
	});

	describe('consolidatePiece', () => {
		it('should open PieceConsolidateDialogComponent and emit refresh on close', () => {
			const dialogRefMock = {
				afterClosed: () => of({ success: true }),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.refresh, 'emit');
			component.sliNumber = 'SLI123';
			component.selection.select(mockPieces[0]);

			component.consolidatePiece();

			expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '60vw',
				autoFocus: false,
				data: {
					pieces: component.selection.selected,
					sliNumber: 'SLI123',
					piceId: '',
				},
			});
			expect(component.refresh.emit).toHaveBeenCalled();
		});

		it('should handle dialog close without result', () => {
			const dialogRefMock = {
				afterClosed: () => of(),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.refresh, 'emit');

			component.consolidatePiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.refresh.emit).not.toHaveBeenCalled(); // Always emits on close
		});
	});

	describe('toggleNode', () => {
		it('should expand node when collapsed and has contained pieces', () => {
			const mockNode: PieceList = {
				...mockPieces[0],
				expanded: false,
				containedPieces: [mockPieces[1]],
			};
			spyOn(component, 'expandNode');

			component.toggleNode(mockNode);

			expect(mockNode.expanded).toBe(true);
			expect(component.expandNode).toHaveBeenCalledWith(mockNode);
		});

		it('should collapse node when expanded', () => {
			const mockNode: PieceList = {
				...mockPieces[0],
				expanded: true,
				containedPieces: [mockPieces[1]],
			};
			spyOn(component, 'collapseNode');

			component.toggleNode(mockNode);

			expect(mockNode.expanded).toBe(false);
			expect(component.collapseNode).toHaveBeenCalledWith(mockNode);
		});

		it('should collapse node when expanded but no contained pieces', () => {
			const mockNode: PieceList = {
				...mockPieces[0],
				expanded: true,
				containedPieces: [],
			};
			spyOn(component, 'collapseNode');

			component.toggleNode(mockNode);

			expect(mockNode.expanded).toBe(false);
			expect(component.collapseNode).toHaveBeenCalledWith(mockNode);
		});
	});

	describe('expandNode', () => {
		it('should add contained pieces to dataSource after the node', () => {
			const containedPiece = { ...mockPieces[1], pieceId: 'CONTAINED1' };
			const mockNode: PieceList = {
				...mockPieces[0],
				containedPieces: [containedPiece],
			};
			component.dataSource.data = [mockNode, mockPieces[1]];

			component.expandNode(mockNode);

			expect(containedPiece.level).toBe(1);
			expect(component.dataSource.data).toEqual([mockNode, containedPiece, mockPieces[1]]);
		});

		it('should return early when node has no contained pieces', () => {
			const mockNode: PieceList = {
				...mockPieces[0],
				containedPieces: undefined,
			};
			const originalData = [...component.dataSource.data];

			component.expandNode(mockNode);

			expect(component.dataSource.data).toEqual(originalData);
		});

		it('should handle multiple contained pieces', () => {
			const containedPiece1 = { ...mockPieces[1], pieceId: 'CONTAINED1' };
			const containedPiece2 = { ...mockPieces[1], pieceId: 'CONTAINED2' };
			const mockNode: PieceList = {
				...mockPieces[0],
				containedPieces: [containedPiece1, containedPiece2],
			};
			component.dataSource.data = [mockNode];

			component.expandNode(mockNode);

			expect(containedPiece1.level).toBe(1);
			expect(containedPiece2.level).toBe(1);
			expect(component.dataSource.data).toEqual([mockNode, containedPiece1, containedPiece2]);
		});
	});

	describe('collapseNode', () => {
		it('should remove contained pieces from dataSource', () => {
			const containedPiece = { ...mockPieces[1], pieceId: 'CONTAINED1' };
			const mockNode: PieceList = {
				...mockPieces[0],
				containedPieces: [containedPiece],
			};
			component.dataSource.data = [mockNode, containedPiece, mockPieces[1]];

			component.collapseNode(mockNode);

			expect(component.dataSource.data).toEqual([mockNode, mockPieces[1]]);
		});

		it('should return early when node has no contained pieces', () => {
			const mockNode: PieceList = {
				...mockPieces[0],
				containedPieces: undefined,
			};
			const originalData = [...component.dataSource.data];

			component.collapseNode(mockNode);

			expect(component.dataSource.data).toEqual(originalData);
		});

		it('should handle multiple contained pieces', () => {
			const containedPiece1 = { ...mockPieces[1], pieceId: 'CONTAINED1' };
			const containedPiece2 = { ...mockPieces[1], pieceId: 'CONTAINED2' };
			const mockNode: PieceList = {
				...mockPieces[0],
				containedPieces: [containedPiece1, containedPiece2],
			};
			component.dataSource.data = [mockNode, containedPiece1, containedPiece2, mockPieces[1]];

			component.collapseNode(mockNode);

			expect(component.dataSource.data).toEqual([mockNode, mockPieces[1]]);
		});
	});

	describe('delPiece', () => {
		it('should stop event propagation and open confirm dialog', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const dialogRefMock = {
				afterClosed: () => of(false),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);

			component.delPiece(eventMock, [mockPieces[0]]);

			expect(eventMock.stopPropagation).toHaveBeenCalled();
			expect(mockDialog.open).toHaveBeenCalled();
			expect(mockTranslateService.instant).toHaveBeenCalledWith('common.dialog.delete.content');
		});

		it('should call deletePiece service and emit refresh when confirmed', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const dialogRefMock = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.refresh, 'emit');
			component.sliNumber = 'SLI123';

			component.delPiece(eventMock, mockPieces);

			expect(mockSliCreateRequestService.deletePiece).toHaveBeenCalledWith(['TEST123', 'TEST456'], 'SLI123');
			expect(component.refresh.emit).toHaveBeenCalled();
		});

		it('should not call deletePiece service when dialog is cancelled', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const dialogRefMock = {
				afterClosed: () => of(false),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.refresh, 'emit');

			component.delPiece(eventMock, mockPieces);

			expect(mockSliCreateRequestService.deletePiece).not.toHaveBeenCalled();
			expect(component.refresh.emit).not.toHaveBeenCalled();
		});

		it('should handle single piece deletion', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const dialogRefMock = {
				afterClosed: () => of(true),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.refresh, 'emit');
			component.sliNumber = 'SLI123';

			component.delPiece(eventMock, [mockPieces[0]]);

			expect(mockSliCreateRequestService.deletePiece).toHaveBeenCalledWith(['TEST123'], 'SLI123');
			expect(component.refresh.emit).toHaveBeenCalled();
		});

		describe('unConsolidatePiece', () => {
			it('should open confirm dialog and call service when confirmed', () => {
				const mockNode: PieceList = {
					...mockPieces[0],
					containedPieces: [{ ...mockPieces[1], pieceId: 'CONTAINED1' }],
				};
				const dialogRefMock = {
					afterClosed: () => of(true),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				spyOn(component.refresh, 'emit');
				component.sliNumber = 'SLI123';
				mockSliCreateRequestService.unConsolidatePiece = jasmine.createSpy('unConsolidatePiece').and.returnValue(of('success'));

				component.unConsolidatePiece(mockNode);

				expect(mockDialog.open).toHaveBeenCalled();
				expect(mockTranslateService.instant).toHaveBeenCalledWith('common.dialog.unConsolidate.conent');
				expect(mockSliCreateRequestService.unConsolidatePiece).toHaveBeenCalledWith({
					pieceId: mockNode.pieceId,
					containedPieceIds: ['CONTAINED1'],
					sliId: 'SLI123',
				});
				expect(component.refresh.emit).toHaveBeenCalled();
			});

			it('should not call service when dialog is cancelled', () => {
				const mockNode: PieceList = {
					...mockPieces[0],
					containedPieces: [mockPieces[1]],
				};
				const dialogRefMock = {
					afterClosed: () => of(false),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				spyOn(component.refresh, 'emit');
				mockSliCreateRequestService.unConsolidatePiece = jasmine.createSpy('unConsolidatePiece');

				component.unConsolidatePiece(mockNode);

				expect(mockSliCreateRequestService.unConsolidatePiece).not.toHaveBeenCalled();
				expect(component.refresh.emit).not.toHaveBeenCalled();
			});

			it('should handle node without contained pieces', () => {
				const mockNode: PieceList = {
					...mockPieces[0],
					containedPieces: undefined,
				};
				const dialogRefMock = {
					afterClosed: () => of(true),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				mockSliCreateRequestService.unConsolidatePiece = jasmine.createSpy('unConsolidatePiece').and.returnValue(of('success'));

				component.unConsolidatePiece(mockNode);

				expect(mockSliCreateRequestService.unConsolidatePiece).toHaveBeenCalledWith({
					pieceId: mockNode.pieceId,
					containedPieceIds: [],
					sliId: component.sliNumber,
				});
			});

			it('should set dataLoading during service call', () => {
				const mockNode: PieceList = {
					...mockPieces[0],
					containedPieces: [mockPieces[1]],
				};
				const dialogRefMock = {
					afterClosed: () => of(true),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				mockSliCreateRequestService.unConsolidatePiece = jasmine.createSpy('unConsolidatePiece').and.returnValue(of('success'));

				component.unConsolidatePiece(mockNode);

				expect(component.dataLoading).toBe(false); // Should be false after completion
			});
		});

		describe('editConsolidatePiece', () => {
			it('should prevent default and open PieceConsolidateDialogComponent', () => {
				const eventMock = jasmine.createSpyObj('Event', ['preventDefault']);
				const mockNode: PieceList = { ...mockPieces[0] };
				const dialogRefMock = {
					afterClosed: () => of({ success: true }),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				spyOn(component.refresh, 'emit');
				component.sliNumber = 'SLI123';

				component.editConsolidatePiece(eventMock, mockNode);

				expect(eventMock.preventDefault).toHaveBeenCalled();
				expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
					width: '60vw',
					autoFocus: false,
					data: {
						pieces: component.selection.selected,
						sliNumber: 'SLI123',
						pieceId: mockNode.pieceId,
					},
				});
				expect(component.refresh.emit).toHaveBeenCalled();
			});
		});

		describe('openHistory', () => {
			it('should open StatusHistoryComponent with correct data', () => {
				const dialogRefMock = {
					afterClosed: () => of(undefined),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);

				component.openHistory('LO123', 'PIECE');

				expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
					width: '60vw',
					autoFocus: false,
					data: {
						loId: 'LO123',
						type: 'PIECE',
					},
				});
			});

			it('should handle different loId and type values', () => {
				const dialogRefMock = {
					afterClosed: () => of(undefined),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);

				component.openHistory('LO456', 'HAWB');

				expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
					width: '60vw',
					autoFocus: false,
					data: {
						loId: 'LO456',
						type: 'HAWB',
					},
				});
			});
		});
	});

	describe('Component Properties and Constants', () => {
		it('should have correct displayedColumns', () => {
			expect(component.displayedColumns).toEqual([
				'select',
				'productDescription',
				'packagingType',
				'grossWeight',
				'dimensions',
				'pieceQuantity',
				'slac',
				'actions',
			]);
		});

		it('should have correct tablePageSizes', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});

		it('should initialize with empty currentSort', () => {
			expect(component.currentSort).toEqual({ active: '', direction: '' });
		});

		it('should initialize dataSource with records', () => {
			expect(component.dataSource).toBeInstanceOf(MatTableDataSource);
		});

		it('should initialize selection model', () => {
			expect(component.selection).toBeDefined();
			expect(component.selection.selected).toBeDefined();
		});

		it('should initialize with correct default values for new properties', () => {
			expect(component.hawbId).toBe('');
			expect(component.hawbNumber).toBe('');
			expect(component.enableInfiniteScroll).toBe(false);
			expect(component.hasMoreData).toBe(true);
			expect(component.dataLoading).toBe(false);
			expect(component.totalSlac).toBe(0);
			expect(component.totalQuantity).toBe(0);
			expect(component.latestStatus).toBe('');
		});
	});

	describe('Input Properties', () => {
		it('should handle sliNumber input', () => {
			const testSliNumber = 'SLI-TEST-123';
			component.sliNumber = testSliNumber;
			expect(component.sliNumber).toBe(testSliNumber);
		});

		it('should handle records input', () => {
			component.records = mockPieces;
			expect(component.records).toEqual(mockPieces);
		});

		it('should handle totalRecords input', () => {
			const testTotal = 100;
			component.totalRecords = testTotal;
			expect(component.totalRecords).toBe(testTotal);
		});

		it('should handle pageParams input', () => {
			const testPageParams = { pageNum: 2, pageSize: 50 };
			component.pageParams = testPageParams;
			expect(component.pageParams).toEqual(testPageParams);
		});

		it('should handle hawbId input', () => {
			const testHawbId = 'HAWB-TEST-456';
			component.hawbId = testHawbId;
			expect(component.hawbId).toBe(testHawbId);
		});

		it('should handle hawbNumber input', () => {
			const testHawbNumber = 'H000789';
			component.hawbNumber = testHawbNumber;
			expect(component.hawbNumber).toBe(testHawbNumber);
		});

		it('should handle enableInfiniteScroll input', () => {
			component.enableInfiniteScroll = true;
			expect(component.enableInfiniteScroll).toBe(true);

			component.enableInfiniteScroll = false;
			expect(component.enableInfiniteScroll).toBe(false);
		});

		it('should handle hasMoreData input', () => {
			component.hasMoreData = false;
			expect(component.hasMoreData).toBe(false);

			component.hasMoreData = true;
			expect(component.hasMoreData).toBe(true);
		});
	});

	describe('Output Events', () => {
		it('should emit saveRequest event', () => {
			spyOn(component.saveRequest, 'emit');
			const testData = { pieceType: 'general', pieceId: 'TEST123' };

			component.saveRequest.emit(testData);

			expect(component.saveRequest.emit).toHaveBeenCalledWith(testData);
		});

		it('should emit sortChange event', () => {
			spyOn(component.sortChange, 'emit');
			const testSort: Sort = { active: 'productDescription', direction: 'asc' };

			component.sortChange.emit(testSort);

			expect(component.sortChange.emit).toHaveBeenCalledWith(testSort);
		});

		it('should emit refresh event', () => {
			spyOn(component.refresh, 'emit');

			component.refresh.emit();

			expect(component.refresh.emit).toHaveBeenCalled();
		});

		it('should emit loadMoreData event', () => {
			spyOn(component.loadMoreData, 'emit');

			component.loadMoreData.emit();

			expect(component.loadMoreData.emit).toHaveBeenCalled();
		});

		it('should emit pagination event with extended properties', () => {
			spyOn(component.pagination, 'emit');
			const testPageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 1,
				pageSize: 20,
				length: 100,
				sortField: 'productDescription',
				sortDirection: 'asc',
			};

			component.pagination.emit(testPageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith(testPageEvent);
		});
	});

	describe('Edge Cases and Error Handling', () => {
		it('should handle empty records array', () => {
			component.records = [];
			const changes = {
				records: new SimpleChange(mockPieces, [], false),
			};

			component.ngOnChanges(changes);

			expect(component.dataSource.data).toEqual([]);
			expect(component.selection.clear).not.toHaveBeenCalled();
		});

		it('should handle sort with empty direction', () => {
			spyOn(component.sortChange, 'emit');
			spyOn(component.pagination, 'emit');
			const sort: Sort = { active: 'productDescription', direction: '' };

			component.onSortChange(sort);

			expect(component.currentSort).toEqual(sort);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sort);
		});

		it('should handle trackByPieceId with different piece objects', () => {
			const piece1: PieceList = { ...mockPieces[0], pieceId: 'UNIQUE1' };
			const piece2: PieceList = { ...mockPieces[0], pieceId: 'UNIQUE2' };

			expect(component.trackByPieceId(piece1)).toBe('UNIQUE1');
			expect(component.trackByPieceId(piece2)).toBe('UNIQUE2');
			expect(component.trackByPieceId(piece1)).not.toBe(component.trackByPieceId(piece2));
		});
	});

	describe('Selection Model Integration', () => {
		it('should handle partial selection correctly', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			component.selection.select(mockPieces[0]);

			expect(component.isAllSelected()).toBe(false);
			expect(selectedItems.length).toBe(1);
		});

		it('should handle selection toggle when partially selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			component.selection.select(mockPieces[0]); // Select only first item
			const selectSpy = component.selection.select;

			component.toggleAllRows();

			expect(selectSpy).toHaveBeenCalled();
			expect(selectedItems.length).toBe(mockPieces.length);
		});
	});

	describe('Dialog Integration', () => {
		it('should pass correct data to AddPieceDialog', () => {
			const dialogRefMock = {
				afterClosed: () => of('general'),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '400px',
				data: {},
			});
		});

		it('should handle multiple dialog results for addPiece', () => {
			const testCases = ['general', 'dg', 'la'];
			spyOn(component.saveRequest, 'emit');

			testCases.forEach((pieceType) => {
				const dialogRefMock = {
					afterClosed: () => of(pieceType),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);

				component.addPiece();

				expect(component.saveRequest.emit).toHaveBeenCalledWith({ pieceType });
			});
		});
	});

	describe('Pagination Integration', () => {
		it('should handle emitPaginationWithSort with different page parameters', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'grossWeight', direction: 'desc' };
			component.pageParams = { pageNum: 3, pageSize: 25 };
			component.totalRecords = 150;

			const customPageEvent: PageEvent = {
				pageIndex: 4,
				pageSize: 100,
				length: 200,
			};

			(component as any).emitPaginationWithSort(customPageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 4,
				pageSize: 100,
				length: 200,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			});
		});

		it('should use default page parameters when no event provided', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'pieceQuantity', direction: 'asc' };
			component.pageParams = { pageNum: 2, pageSize: 50 };
			component.totalRecords = 75;

			(component as any).emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 1, // pageNum - 1
				pageSize: 50,
				length: 75,
				sortField: 'pieceQuantity',
				sortDirection: 'asc',
			});
		});

		describe('dataLoading property integration', () => {
			it('should set dataLoading to true during delPiece service call', () => {
				const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
				const dialogRefMock = {
					afterClosed: () => of(true),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				component.sliNumber = 'SLI123';
				component.dataLoading = false;

				component.delPiece(eventMock, [mockPieces[0]]);

				// After the service call completes, dataLoading should be false
				expect(component.dataLoading).toBe(false);
			});

			it('should set dataLoading to true during unConsolidatePiece service call', () => {
				const mockNode: PieceList = {
					...mockPieces[0],
					containedPieces: [mockPieces[1]],
				};
				const dialogRefMock = {
					afterClosed: () => of(true),
				};
				mockDialog.open.and.returnValue(dialogRefMock as any);
				mockSliCreateRequestService.unConsolidatePiece = jasmine.createSpy('unConsolidatePiece').and.returnValue(of('success'));
				component.dataLoading = false;

				component.unConsolidatePiece(mockNode);

				// After the service call completes, dataLoading should be false
				expect(component.dataLoading).toBe(false);
			});
		});

		describe('Infinite scroll integration', () => {
			it('should handle scroll event when all conditions are false', () => {
				component.enableInfiniteScroll = false;
				component.dataLoading = true;
				component.hasMoreData = false;
				spyOn(component.loadMoreData, 'emit');

				const mockEvent = {
					target: {
						scrollTop: 100,
						scrollHeight: 200,
						clientHeight: 100,
					} as HTMLDivElement,
				} as unknown as Event;

				component.onTableScroll(mockEvent);

				expect(component.loadMoreData.emit).not.toHaveBeenCalled();
			});

			it('should handle scroll calculation edge cases', () => {
				component.enableInfiniteScroll = true;
				component.dataLoading = false;
				component.hasMoreData = true;
				spyOn(component.loadMoreData, 'emit');

				// Test when scrollHeight - (scrollTop + clientHeight) > 0 (not at bottom)
				const mockEvent = {
					target: {
						scrollTop: 99,
						scrollHeight: 200,
						clientHeight: 100, // 99 + 100 = 199, which is < 200
					} as HTMLDivElement,
				} as unknown as Event;

				component.onTableScroll(mockEvent);

				expect(component.loadMoreData.emit).not.toHaveBeenCalled();
			});
		});
	});

	describe('Additional Coverage Tests', () => {
		it('should handle selection state correctly', () => {
			// Test that selection works with our mock
			selectedItems.length = 0;
			component.selection.select(mockPieces[0]);

			expect(selectedItems.length).toBe(1);
			expect(selectedItems[0]).toBe(mockPieces[0]);
		});

		it('should handle multiple piece types in editPiece', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			spyOn(component.saveRequest, 'emit');

			// Test each piece type
			const testCases = [
				{ type: PieceType.GENERAL, expected: 'general' },
				{ type: PieceType.DANGEROUS_GOODS, expected: 'dg' },
				{ type: PieceType.LIVE_ANIMALS, expected: 'la' },
				{ type: 'Unknown' as any, expected: '' },
			];

			testCases.forEach(({ type, expected }) => {
				const piece = { ...mockPieces[0], type };

				component.editPiece(eventMock, piece);

				expect(component.saveRequest.emit).toHaveBeenCalledWith({
					pieceType: expected,
					pieceId: piece.pieceId,
				});
			});
		});

		it('should handle dialog close without result in addPiece', () => {
			const dialogRefMock = {
				afterClosed: () => of(null),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.saveRequest, 'emit');

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.saveRequest.emit).not.toHaveBeenCalled();
		});

		it('should handle dialog close with undefined result in addPiece', () => {
			const dialogRefMock = {
				afterClosed: () => of(undefined),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.saveRequest, 'emit');

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.saveRequest.emit).not.toHaveBeenCalled();
		});
	});
});
