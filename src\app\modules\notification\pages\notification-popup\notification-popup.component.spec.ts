import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotificationPopupComponent } from './notification-popup.component';
import { TranslateModule } from '@ngx-translate/core';

describe('NotificationTipComponent', () => {
	let component: NotificationPopupComponent;
	let fixture: ComponentFixture<NotificationPopupComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [NotificationPopupComponent, TranslateModule.forRoot()],
		}).compileComponents();

		fixture = TestBed.createComponent(NotificationPopupComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
