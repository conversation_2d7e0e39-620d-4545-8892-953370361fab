// src/app/components/generic-table/generic-table.component.ts
import {
	Component,
	Input,
	ViewChild,
	AfterViewInit,
	Output,
	EventEmitter,
	OnChanges,
	SimpleChanges,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from '@angular/core';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { SpinnerComponent } from '../spinner/spinner.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
	selector: 'orll-table',
	templateUrl: './orll-table.component.html',
	styleUrls: ['./orll-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [TranslateModule, MatTableModule, MatPaginatorModule, SpinnerComponent, MatSortModule, MatIconModule, MatButtonModule],
})
export class OrllTableComponent<T> implements AfterViewInit, OnChanges {
	@Input() service!: GenericTableService<T>;
	@Input() columns: OrllColumnDef<T>[] = [];
	@Input() hasPagination = true;
	@Input() param: any;
	@Output() rowClick = new EventEmitter<T>();

	displayedColumns: string[] = [];
	dataSource = new MatTableDataSource<T>();
	totalRecords = 0;
	pageSize = 10;
	pageIndex = 0;
	sortField = '';
	sortDirection: 'asc' | 'desc' = 'asc';
	readonly tablePageSizes: number[] = [10, 50, 100];

	queryParam: any;
	private isViewInit = false;

	dataLoading = false;

	@ViewChild(MatPaginator) paginator!: MatPaginator;
	@ViewChild(MatSort) sort!: MatSort;

	constructor(private readonly cdr: ChangeDetectorRef) {}

	ngAfterViewInit(): void {
		this.displayedColumns = this.columns.map((col) => col.key as string);
		this.dataSource.paginator = this.paginator;
		this.dataSource.sort = this.sort;
		this.isViewInit = true;
		this.loadData();
	}

	ngOnChanges(changes: SimpleChanges): void {
		// param obj will be changed.
		if (changes['param']) {
			this.queryParam = { ...this.param };
			if (this.isViewInit) {
				this.loadData();
			}
		}
	}

	loadData(): void {
		this.dataLoading = true;
		if (this.hasPagination) {
			let request = {
				pageNum: this.pageIndex + 1,
				pageSize: this.pageSize,
				orderByColumn: this.sortField,
				isAsc: this.sortDirection,
			};

			if (this.param) {
				request = { ...request, ...this.queryParam };
			}

			this.service.getDataPerPage(request).subscribe({
				next: (response) => {
					this.dataLoading = false;
					this.dataSource.data = response.rows;
					this.totalRecords = response.total;
					this.paginator.length = this.totalRecords;
					this.cdr.detectChanges();
				},
				error: (err) => {
					console.error('orll table data retriving error', err);
					this.dataLoading = false;
				},
			});
		} else {
			const request = this.param || {};
			this.service.loadAllData(request).subscribe({
				next: (res) => {
					this.dataSource.data = res;
					this.dataLoading = false;
					this.cdr.detectChanges();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
		}
	}

	onPageChange(event: any): void {
		this.pageIndex = event.pageIndex;
		this.pageSize = event.pageSize;
		this.loadData();
	}

	onSortChange(sortState: Sort): void {
		if (sortState.direction) {
			this.sortField = sortState.active;
			this.sortDirection = sortState.direction;
		} else {
			this.sortField = '';
			this.sortDirection = 'asc';
		}
		this.pageIndex = 0;
		this.loadData();
	}

	onRowClicked(row: T): void {
		this.rowClick.emit(row);
	}

	getCellValue(column: OrllColumnDef<T>, row: T): any {
		let value: any;
		if (column.accessor) {
			value = column.accessor(row);
		} else {
			value = row[column.key as keyof T];
		}
		return column.transform ? column.transform(value) : value;
	}
}
