import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { SubscriptionRequestService } from '../../services/subscription-request.service';
import { EventType, SubscriptionDialogData } from '../../models/subscription.model';
import { TranslateModule } from '@ngx-translate/core';
import { convertJsonStrToObj } from '@shared/utils/common.utils';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';

@Component({
	selector: 'orll-subscription-request-details',
	imports: [
		CommonModule,
		MatIconModule,
		MatButtonModule,
		TranslateModule,
		SpinnerComponent,
		MatInputModule,
		MatListModule,
		MatDialogModule,
	],
	templateUrl: './subscription-request-details.component.html',
	styleUrl: './subscription-request-details.component.scss',
})
export class SubscriptionRequestDetailsComponent extends RolesAwareComponent implements OnInit {
	ispublisher = false;
	subscriptionDetails: any;
	dataLoading = false;
	eventList: EventType[] = [];
	permissions: EventType[] = [];

	constructor(
		private readonly subscriptionService: SubscriptionRequestService,
		private readonly dialogRef: MatDialogRef<SubscriptionRequestDetailsComponent>,
		@Inject(MAT_DIALOG_DATA) public data: SubscriptionDialogData
	) {
		super();
	}

	loadSubscriptionDetails(id: string) {
		this.dataLoading = true;
		this.subscriptionService.getSubscriptionsDetails(id).subscribe({
			next: (res) => {
				this.subscriptionDetails = res;
				if (res.includeSubscriptionEventType) {
					this.eventList = convertJsonStrToObj(res.includeSubscriptionEventType.toString());
				}

				// permissions
				if (res.permissions) {
					this.permissions = convertJsonStrToObj(res.permissions.toString());
				}
				this.dataLoading = false;
			},
			error: (err) => {
				this.dataLoading = false;
				console.error('get subscription request failed', err);
			},
		});
	}

	ngOnInit(): void {
		this.loadSubscriptionDetails(this.data.id);

		this.getCurrentUser().subscribe((user) => {
			this.ispublisher = user?.orgId !== this.data.orgid;
		});
	}

	updateSubscription(status: string): void {
		this.subscriptionService.updateSubscriptionsStatus(this.data.id, status).subscribe({
			next: () => {
				this.dialogRef.close(true);
			},
			error: (err) => {
				console.error('update failed', err);
				this.dialogRef.close(false);
			},
		});
	}
}
