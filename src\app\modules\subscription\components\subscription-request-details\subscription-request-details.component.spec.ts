import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SubscriptionRequestDetailsComponent } from './subscription-request-details.component';
import { of } from 'rxjs';
import { SubscriptionRequestService } from '../../services/subscription-request.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UserProfileService } from '@shared/services/user-profile.service';
import { TranslateModule } from '@ngx-translate/core';
import { SubscriptionDetailObj } from '../../models/subscription.model';
import { UserProfile } from '@shared/models/user-profile.model';

const mockMatDialogRef = {
	close: jasmine.createSpy('close'),
};

function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

const resGlobal: SubscriptionDetailObj = {
	includeSubscriptionEventType: '[{ "eventName": "aaa", "checked": "true" }]',
	id: '',
	publisherOrgId: '',
	subscriptionRequestUri: '',
	orgId: '',
	status: '',
	isRequestedBy: '',
	isRequestedAt: '',
	permissions: '[]',
	topic: '',
	topicType: '',
	createDate: '',
	approvedDate: '',
	subscriptionType: '',
	subscriberOrgName: '',
};

describe('SubscriptionRequestDetailsComponent', () => {
	let component: SubscriptionRequestDetailsComponent;
	let fixture: ComponentFixture<SubscriptionRequestDetailsComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionRequestService>;
	let mockUserProfileService: jasmine.SpyObj<UserProfileService>;

	const mockData = {
		id: 'sub-123',
		orgid: 'org-456',
		status: 'PENDING',
		orgMap: new Map<string, string>([['key1', 'value1']]),
	};

	const mockDetailResponse: SubscriptionDetailObj = {
		topic: '111',
		topicType: '111',
		createDate: '',
		approvedDate: '',
		subscriptionType: '',
		status: '',
		isRequestedBy: '',
		isRequestedAt: '',
		subscriberOrgName: '',
		orgId: '',
		includeSubscriptionEventType: '[{ "eventName": "aaa", "checked": "true" }]',
		permissions: '[{ "eventName": "aaa", "checked": "true" }]',
		publisherOrgId: '',
		id: '',
		subscriptionRequestUri: '',
	};

	beforeEach(async () => {
		// Mock Services
		mockSubscriptionService = jasmine.createSpyObj<SubscriptionRequestService>('SubscriptionService', [
			'getSubscriptionsDetails',
			'updateSubscriptionsStatus',
		]);
		const res = {
			topic: '111',
			topicType: '111',
			createDate: '',
			approvedDate: '',
			subscriptionType: '',
			status: '',
			isRequestedBy: '',
			isRequestedAt: '',
			subscriberOrgName: '',
			orgId: '',
			includeSubscriptionEventType: '[{ "eventName": "aaa", "checked": "true" }]',
			permissions: '[{ "eventName": "aaa", "checked": "true" }]',
			publisherOrgId: '',
			id: '',
			subscriptionRequestUri: '',
		};
		mockSubscriptionService.getSubscriptionsDetails.and.returnValue(of(res));
		mockSubscriptionService.updateSubscriptionsStatus.and.returnValue(of(res));
		mockUserProfileService = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		mockUserProfileService.hasPermission.and.returnValue(of(true));
		mockUserProfileService.hasSomeRole.and.returnValue(of(true));
		const userProfile: UserProfile = {
			userId: '',
			email: '',
			firstName: '',
			lastName: '',
			primaryOrgId: '',
			primaryOrgName: '',
			orgId: 'OrgId-1',
			orgName: '',
			orgType: '',
			userType: '',
			menuList: [],
			permissionList: [],
			orgList: [],
		};
		mockUserProfileService.getProfile.and.returnValue(of(userProfile));

		await TestBed.configureTestingModule({
			imports: [SubscriptionRequestDetailsComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: SubscriptionRequestService, useValue: mockSubscriptionService },
				{ provide: UserProfileService, useValue: mockUserProfileService },
				{ provide: MatDialogRef, useValue: mockMatDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockData },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SubscriptionRequestDetailsComponent);
		const mockUser = createMockUserProfile({ primaryOrgId: 'org123' });

		component = fixture.componentInstance;
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call loadDetail on ngOnInit with data.id', () => {
		spyOn(component, 'loadSubscriptionDetails');
		component.ngOnInit();
		expect(component.loadSubscriptionDetails).toHaveBeenCalledWith('sub-123');
	});

	it('should set ispublisher based on orgId comparison', () => {
		fixture = TestBed.createComponent(SubscriptionRequestDetailsComponent);
		component = fixture.componentInstance;
		const mockUser = createMockUserProfile({ primaryOrgId: 'org-789', orgId: 'org-789' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser));
		component.data = mockData;
		fixture.detectChanges();

		expect(component.ispublisher).toBe(true); // 'org-789' !== 'org-456'

		fixture = TestBed.createComponent(SubscriptionRequestDetailsComponent);
		component = fixture.componentInstance;
		const mockUser1 = createMockUserProfile({ primaryOrgId: 'org-456', orgId: 'org-456' });
		spyOn(component, 'getCurrentUser').and.returnValue(of(mockUser1));
		component.data = mockData;
		fixture.detectChanges();

		expect(component.ispublisher).toBe(false);
	});

	it('should load subscription detail and populate data sources', () => {
		component.loadSubscriptionDetails('sub-123');
		fixture.detectChanges();

		expect(component.subscriptionDetails).toEqual(mockDetailResponse);
		expect(component.eventList).toEqual([{ eventName: 'aaa', checked: 'true' }]);
		expect(component.permissions).toEqual([{ eventName: 'aaa', checked: 'true' }]);
		expect(component.dataLoading).toBe(false);
	});

	it('should handle empty events array', () => {
		const res = {
			topic: '111',
			topicType: '111',
			createDate: '',
			approvedDate: '',
			subscriptionType: '',
			status: '',
			isRequestedBy: '',
			isRequestedAt: '',
			subscriberOrgName: '',
			orgId: '',
			includeSubscriptionEventType: '[]',
			permissions: '[{ "eventName": "bbb", "checked": "true" }]',
			publisherOrgId: '',
			id: '',
			subscriptionRequestUri: '',
		};
		mockSubscriptionService.getSubscriptionsDetails.and.returnValue(of(res));

		component.loadSubscriptionDetails('sub-123');
		fixture.detectChanges();

		expect(component.eventList).toEqual([]);
		expect(component.permissions).toEqual([{ eventName: 'bbb', checked: 'true' }]);
	});

	it('should handle array-type events and permissions', () => {
		const res = {
			topic: '111',
			topicType: '111',
			createDate: '',
			approvedDate: '',
			subscriptionType: '',
			status: '',
			isRequestedBy: '',
			isRequestedAt: '',
			subscriberOrgName: '',
			orgId: '',
			includeSubscriptionEventType: '[{ "eventName": "Event1", "checked": "true" }]',
			permissions: '[{ "eventName": "Event1", "checked": "true" }]',
			publisherOrgId: '',
			id: '',
			subscriptionRequestUri: '',
		};
		mockSubscriptionService.getSubscriptionsDetails.and.returnValue(of(res));

		component.loadSubscriptionDetails('sub-123');
		fixture.detectChanges();

		expect(component.eventList).toEqual([{ eventName: 'Event1', checked: 'true' }]);
		expect(component.permissions).toEqual([{ eventName: 'Event1', checked: 'true' }]);
	});

	it('should not update dataSource if includeSubscriptionEventType is false', () => {
		const res: SubscriptionDetailObj = {
			includeSubscriptionEventType: '[{"eventName":"logistic_object_create","checked":"true"}]',
			id: '',
			publisherOrgId: '',
			subscriptionRequestUri: '',
			orgId: '',
			status: '',
			isRequestedBy: '',
			isRequestedAt: '',
			permissions: '[]',
			topic: '',
			topicType: '',
			createDate: '',
			approvedDate: '',
			subscriptionType: '',
			subscriberOrgName: '',
		};
		mockSubscriptionService.getSubscriptionsDetails.and.returnValue(of(res));

		component.loadSubscriptionDetails('sub-123');
		fixture.detectChanges();

		expect(component.eventList).toEqual([
			{
				eventName: 'logistic_object_create',
				checked: 'true',
			},
		]);
	});

	it('should emit refreshParent and close dialog with true on deSubscribe success', () => {
		mockSubscriptionService.updateSubscriptionsStatus.and.returnValue(of(resGlobal));

		component.updateSubscription('UNSUBSCRIBED');
		expect(mockSubscriptionService.updateSubscriptionsStatus).toHaveBeenCalledWith('sub-123', 'UNSUBSCRIBED');
		expect(mockMatDialogRef.close).toHaveBeenCalledWith(true);
	});
});
