import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SubscriptionConfigurationDetailComponent } from './subscription-configuration-detail.component';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { DictObj, SubscriptionConfigurationListObj } from '../../models/subscription.model';
import { of } from 'rxjs';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

describe('SubscriptionConfigurationDetailComponent', () => {
	let component: SubscriptionConfigurationDetailComponent;
	let fixture: ComponentFixture<SubscriptionConfigurationDetailComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionConfigurationService>;
	let dialogRefSpy: jasmine.SpyObj<MatDialogRef<SubscriptionConfigurationListObj>>;

	let formBuilder: FormBuilder;
	const mockDetail: SubscriptionConfigurationListObj = {
		id: '',
		topic: 'news',
		topicType: 'email',
		subscriptionType: '',
		subscriptionEventType: '[{"eventName": "login", "checked": "true"},{"eventName": "logout", "checked": "false"}]',
		description: '',
		expiresAt: '',
		userId: '',
		orgId: '',
		subscriberId: '',
		createAt: '',
		subscriptionRequestUri: '',
	};

	const mockEventTypes: DictObj[] = [
		{ code: 'login', name: 'User Login' },
		{ code: 'logout', name: 'User Logout' },
		{ code: 'update', name: 'Object Update' },
	];

	beforeEach(async () => {
		dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close', 'afterClosed']);
		dialogRefSpy.afterClosed.and.returnValue(of(true));
		mockSubscriptionService = jasmine.createSpyObj('SubscriptionConfigurationService', [
			'getTopicTypeOptions',
			'getTopicOptions',
			'saveConfiguration',
			'getEventTypeOptions',
		]);
		mockSubscriptionService.getTopicTypeOptions.and.returnValue(of([]));
		mockSubscriptionService.getTopicOptions.and.returnValue(of([]));
		mockSubscriptionService.getEventTypeOptions.and.returnValue(of([]));
		mockSubscriptionService.saveConfiguration.and.returnValue(of(mockDetail));
		await TestBed.configureTestingModule({
			imports: [SubscriptionConfigurationDetailComponent, ReactiveFormsModule, TranslateModule.forRoot()],
			providers: [
				FormBuilder,
				{ provide: SubscriptionConfigurationService, useValue: mockSubscriptionService },
				{ provide: MAT_DIALOG_DATA, useValue: mockDetail },
				{ provide: MatDialogRef, useValue: dialogRefSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SubscriptionConfigurationDetailComponent);
		formBuilder = TestBed.inject(FormBuilder);
		component = fixture.componentInstance;
		component.configurationForm = formBuilder.group({
			topicType: [''],
			topic: [''],
			description: [''],
			expiresAt: [''],
			subscriptionEventType: formBuilder.group({ test: [''] }, [component.atLeastOneChecked()]),
		});

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('buildEventTypeFormGroup', () => {
		it('should dynamically add controls to subscriptionEventType group', () => {
			component.buildEventTypeFormGroup(mockEventTypes);

			const eventsGroup = component.configurationForm.get('subscriptionEventType') as FormGroup;
			expect(eventsGroup).toBeTruthy();
			expect(eventsGroup.contains('login')).toBe(true);
			expect(eventsGroup.contains('logout')).toBe(true);
			expect(eventsGroup.contains('update')).toBe(true);
			expect(eventsGroup.get('login')?.value).toBe(false);
		});
	});

	describe('atLeastOneChecked validator', () => {
		let validatorFn: any;
		let eventsGroup: FormGroup;

		beforeEach(() => {
			validatorFn = component.atLeastOneChecked();
			eventsGroup = formBuilder.group({
				login: [false],
				logout: [false],
			});
		});

		it('should return null when at least one is checked', () => {
			eventsGroup.patchValue({ login: true });
			const result = validatorFn(eventsGroup);
			expect(result).toBeNull();
		});

		it('should return error when none is checked', () => {
			const result = validatorFn(eventsGroup);
			expect(result).toEqual({ atLeastOne: true });
		});
	});

	describe('getConfiguration', () => {
		it('should patch form values and set event type checks correctly', () => {
			component.buildEventTypeFormGroup([
				{ code: 'login', name: 'login' },
				{ code: 'logout', name: 'logout' },
			]);

			component.getConfiguration(mockDetail);

			const eventsGroup = component.configurationForm.get('subscriptionEventType') as FormGroup;

			expect(component.configurationForm.get('topicType')?.value).toBe('email');
			expect(component.configurationForm.get('topic')?.value).toBe('news');
			expect(eventsGroup.get('login')?.value).toBe(true);
			expect(eventsGroup.get('logout')?.value).toBe(false);
		});

		it('should do nothing if detail is falsy', () => {
			const spy = spyOn(component.configurationForm, 'patchValue');
			component.getConfiguration(null!);
			expect(spy).not.toHaveBeenCalled();
		});
	});

	describe('saveConfiguration', () => {
		beforeEach(() => {
			component.configurationForm.patchValue({ type: 'email', topic: 'news' });
			component.buildEventTypeFormGroup([
				{
					code: 'login',
					name: '',
				},
			]);
			component.configurationForm.get('subscriptionEventType')?.patchValue({ login: true });
		});

		it('should mark all as touched if form is invalid', () => {
			component.configurationForm.setErrors({ invalid: true });
			const spy = spyOn(component.configurationForm, 'markAllAsTouched');

			component.saveConfiguration();

			expect(spy).toHaveBeenCalled();
			expect(component.dataLoading).toBe(false);
		});
	});
});
