import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { StatusHistoryComponent } from './status-history.component';
import { MawbStatusService } from '../../services/mawb-status.service';
import { HistoryDialogData, StatusHistory } from '../../models/mawb-event.model';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';

describe('StatusHistoryComponent', () => {
	let component: StatusHistoryComponent;
	let fixture: ComponentFixture<StatusHistoryComponent>;
	let statusService: jasmine.SpyObj<MawbStatusService>;
	const mockDialogRef = jasmine.createSpyObj(['close']);

	const mockDialogData: HistoryDialogData = {
		loId: '123',
		type: 'shipment',
	};

	beforeEach(() => {
		const statusServiceSpy = jasmine.createSpyObj('MawbStatusService', ['getStatusHistoryList']);

		TestBed.configureTestingModule({
			imports: [StatusHistoryComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbStatusService, useValue: statusServiceSpy },
				{ provide: 'MAT_DIALOG_DATA', useValue: mockDialogData },
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		fixture = TestBed.createComponent(StatusHistoryComponent);
		component = fixture.componentInstance;
		statusService = TestBed.inject(MawbStatusService) as jasmine.SpyObj<MawbStatusService>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call initData on ngOnInit', () => {
		const mockData: StatusHistory[] = [
			{
				latestStatus: 'Created',
				eventDate: '2025-01-01',
				userName: 'Admin',
				eventTimeType: 'System',
				partialEventIndicator: false,
				orgName: '',
				eventLoId: '',
			},
		];

		statusService.getStatusHistoryList.and.returnValue(of(mockData));

		fixture.detectChanges();

		expect(statusService.getStatusHistoryList).toHaveBeenCalledWith(mockDialogData.loId, mockDialogData.type);
		expect(component.dataSource.data).toEqual(mockData);
		expect(component.dataLoading).toBeFalse();
	});

	it('should call getHistory with correct params and update dataSource', () => {
		const mockData: StatusHistory[] = [
			{
				latestStatus: 'Created',
				eventDate: '2025-01-01',
				userName: 'Admin',
				eventTimeType: 'System',
				partialEventIndicator: true,
				orgName: '',
				eventLoId: '',
			},
		];

		statusService.getStatusHistoryList.and.returnValue(of(mockData));

		component['initData']();

		expect(statusService.getStatusHistoryList).toHaveBeenCalledWith(mockDialogData.loId, mockDialogData.type);
		expect(component.dataSource.data).toEqual(mockData);
		expect(component.dataLoading).toBeFalse();
	});

	it('should handle error and set dataLoading to false', () => {
		statusService.getStatusHistoryList.and.returnValue(throwError(() => new Error('API Error')));

		component['initData']();

		expect(statusService.getStatusHistoryList).toHaveBeenCalled();
		expect(component.dataLoading).toBeFalse();
	});

	it('should have correct displayedColumns', () => {
		const expectedColumns = ['event', 'updateTime', 'updateBy', 'eventTimeType', 'partialEvent'];
		expect(component.displayedColumns).toEqual(expectedColumns);
	});
});
