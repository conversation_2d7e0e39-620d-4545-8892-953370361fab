import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	SimpleChanges,
} from '@angular/core';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { PageEvent } from '@angular/material/paginator';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { HawbSearchComponent } from '../../components/hawb-search/hawb-search.component';
import { HawbTableComponent } from '../../components/hawb-table/hawb-table.component';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { HawbSearchPayload } from '../../models/hawb-search-payload.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Sort } from '@angular/material/sort';
import { ShareType } from '@shared/models/share-type.model';
import { ShareDialogComponent } from '@shared/components/share-dialog/share-dialog.component';
import { MawbSearchRequestService } from 'src/app/modules/mawb-mgmt/services/mawb-search-request.service';
import { DelegationRequestComponent } from '@shared/components/delegation-request/delegation-request.component';

const HAWB_TAB_INDEX = 1;

@Component({
	selector: 'orll-hawb-list-page',
	templateUrl: './hawb-list-page.component.html',
	styleUrl: './hawb-list-page.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [HawbSearchComponent, HawbTableComponent, MatIconModule, TranslateModule, SpinnerComponent],
})
export default class HawbListPageComponent extends DelegationRequestComponent implements OnInit, OnChanges {
	@Input() fromCreateMawb = false;
	@Input() isFHL = false;
	@Input() hawbIdList: string[] = [];
	@Input() mawbId = '';
	@Input() selectedTabIndex = 0;

	@Output() hawbListChange = new EventEmitter<HawbListObject[]>();
	@Output() refreshDelegationRequest = new EventEmitter<void>();

	hawbSearchPayload!: HawbSearchPayload;
	hawbList: HawbListObject[] = [];
	hawbListTotalRecords = 0;
	totalQuantity = 0;
	totalSlac = 0;
	dataLoading = false;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	constructor(
		private readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly mawbSearchRequestService: MawbSearchRequestService,
		private readonly cdr: ChangeDetectorRef,
		private readonly translate: TranslateService
	) {
		super();
	}

	ngOnInit(): void {
		if (!this.isFHL) this.getHawbListPage(this.pageParams);
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['selectedTabIndex']) {
			if (this.selectedTabIndex === HAWB_TAB_INDEX && this.isFHL) {
				if (this.hawbIdList.length) {
					this.hawbSearchPayload = {
						hawbIdList: this.hawbIdList,
					};
				}

				if (this.mawbId) {
					this.hawbSearchPayload = {
						mawbIdList: [this.mawbId],
					};
				}

				this.getHawbListPage(this.pageParams);
			}
		}
	}

	onSearch(hawbSearchPayload: HawbSearchPayload): void {
		this.hawbSearchPayload = hawbSearchPayload;
		this.getHawbListPage(this.pageParams);
	}

	onHawbListSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
		this.getHawbListPage(this.pageParams);
	}

	onHawbListObjectShare(event: HawbListObject): void {
		this.dialog.open(ShareDialogComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				title: this.translate.instant('hawb.share.title'),
				shareType: ShareType.HAWB,
				param: event.hawbId,
			},
		});
	}

	onHawbListPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getHawbListPage(this.pageParams);
	}

	private getHawbListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.hawbList = [];

		if (this.fromCreateMawb && !this.mawbId && !this.hawbIdList?.length) {
			if (!this.hawbSearchPayload) {
				this.hawbSearchPayload = {};
			}
			this.hawbSearchPayload.existMawb = false;
		}

		this.hawbSearchRequestService.getHawbList(pageParams, this.hawbSearchPayload).subscribe({
			next: (res) => {
				this.hawbList = res.rows;
				this.hawbListTotalRecords = res.total;
				this.dataLoading = false;
				this.cdr.markForCheck();

				if (this.isFHL) {
					this.hawbListChange.emit(this.hawbList);

					if (this.mawbId) {
						this.dataLoading = true;
						this.mawbSearchRequestService.getTotalPieceQuantity(this.mawbId).subscribe((res) => {
							this.totalQuantity = res.totalQuantity;
							this.totalSlac = res.totalSlac;

							this.dataLoading = false;
							this.cdr.markForCheck();
						});
					}
				}
			},
			error: (err) => {
				this.dataLoading = false;

				// trigger delegation request when 403
				if (err.status === 403) {
					this.delegationRequest(err.error, this.mawbId)
						.pipe(takeUntilDestroyed(this.destroyRef))
						.subscribe((result) => {
							if (result) {
								this.refreshDelegationRequest.emit();
							}
						});
				}
			},
		});
	}
}
