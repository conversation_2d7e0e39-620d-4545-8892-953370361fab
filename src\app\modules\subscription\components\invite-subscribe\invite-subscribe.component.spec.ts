import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InviteSubscribeComponent } from './invite-subscribe.component';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { DictObj } from '../../models/subscription.model';
import { TranslateModule } from '@ngx-translate/core';

describe('InviteSubscribeComponent', () => {
	let component: InviteSubscribeComponent;
	let fixture: ComponentFixture<InviteSubscribeComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionConfigurationService>;
	let mockOrgMgmtRequestService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockDialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		mockSubscriptionService = jasmine.createSpyObj('SubscriptionService', [
			'getTopicTypeOptions',
			'getTopicOptions',
			'inviteToSubscribe',
		]);

		mockOrgMgmtRequestService = jasmine.createSpyObj<OrgMgmtRequestService>('OrgMgmtRequestService', ['getOrgInfo', 'getOrgList']);
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockOrgMgmtRequestService.getOrgList.and.returnValue(of([{ id: '111', name: '111', orgType: '1111' }]));

		await TestBed.configureTestingModule({
			imports: [InviteSubscribeComponent, MatDialogModule, ReactiveFormsModule, TranslateModule.forRoot()],
			providers: [
				NonNullableFormBuilder,
				{ provide: SubscriptionConfigurationService, useValue: mockSubscriptionService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
				{ provide: MatDialogRef, useValue: { close: jasmine.createSpy('close') } },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(InviteSubscribeComponent);
		component = fixture.componentInstance;

		// Mock service responses
		mockSubscriptionService.getTopicTypeOptions.and.returnValue(of([]));
		mockSubscriptionService.getTopicOptions.and.returnValue(of([]));
		mockSubscriptionService.inviteToSubscribe.and.returnValue(of());

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should load data on init', () => {
		const testData: DictObj[] = [{ name: '1', code: 'Test' }];
		const topics: string[] = ['111', '222'];
		const testData1: Organization[] = [{ id: '1', name: 'Test', orgType: 'test', prefix: 'test' }];
		mockSubscriptionService.getTopicTypeOptions.and.returnValue(of(testData));
		mockSubscriptionService.getTopicOptions.and.returnValue(of(topics));
		mockOrgMgmtRequestService.getOrgList.and.returnValue(of(testData1));

		component.ngOnInit();

		expect(mockSubscriptionService.getTopicTypeOptions).toHaveBeenCalled();
		expect(mockSubscriptionService.getTopicOptions).toHaveBeenCalled();
		expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
	});

	it('should call inviteSbuScriber with identifyTopic when type is LOGISTICS_OBJECT_IDENTIFIER', () => {
		const logisticsType = 'https://api#LOGISTICS_OBJECT_IDENTIFIER';
		component.inviteForm.patchValue({
			topicType: logisticsType,
			topic: 'test-topic',
			subscribers: ['test-subscriber'],
		});

		component.inviteToSubscribe();

		expect(mockSubscriptionService.inviteToSubscribe).toHaveBeenCalledWith('test-topic', logisticsType, ['test-subscriber']);
	});

	it('should call inviteSbuScriber with selectTopic when type is not LOGISTICS_OBJECT_IDENTIFIER', () => {
		component.inviteForm.patchValue({
			topicType: 'OTHER_TYPE',
			topic: 'regular-topic',
			subscribers: ['test-subscriber'],
		});

		component.inviteToSubscribe();

		expect(mockSubscriptionService.inviteToSubscribe).toHaveBeenCalledWith('regular-topic', 'OTHER_TYPE', ['test-subscriber']);
	});
});
