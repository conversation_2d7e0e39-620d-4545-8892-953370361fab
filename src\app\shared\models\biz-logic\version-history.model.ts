export interface VersionDialogData {
	actionRequestUri: string;
	title: string;
}

export interface VersionHistoryObj {
	loType: string;
	loId: string;
	actionRequestUri: string;
	version: string;
	actionRequestDate: string;
	updateOrgName: string;
	requestStatus: RequestStatus;
}

export interface OperationObj {
	id: string;
	loType: string;
	property: string;
	oldValue: string;
	newValue: string;
}

export interface VersionHistoryDetailObj {
	id: string;
	hasLogisticsObject: boolean;
	hasOperation: OperationObj[];
	actionRequestDate: string;
	updateOrgName: string;
	requestStatus: RequestStatus;
	description: string;
	owner: boolean;
}

export enum RequestStatus {
	REQUEST_ACCEPTED = 'Approved',
	REQUEST_REJECTED = 'Rejected',
	REQUEST_FAILED = 'Failed',
	REQUEST_REVOKED = 'Revoked',
}

export enum RequestStatusChangeAction {
	REQUEST_ACCEPTED = 'REQUEST_ACCEPTED',
	REQUEST_REJECTED = 'REQUEST_REJECTED',
	REQUEST_FAILED = 'REQUEST_FAILED',
	REQUEST_REVOKED = 'REQUEST_REVOKED',
}
