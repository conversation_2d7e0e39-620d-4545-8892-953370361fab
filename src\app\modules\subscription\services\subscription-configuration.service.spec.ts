import { TestBed } from '@angular/core/testing';
import { SubscriptionConfigurationService } from './subscription-configuration.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { DictObj, SubscriptionConfigDetailObj, SubscriptionConfigurationListObj } from '../models/subscription.model';
import { environment } from '@environments/environment';
import { PaginationResponse } from '@shared/models/pagination-response.model';

const baseUrl = environment.baseApi;

describe('SubscriptionConfigurationService', () => {
	let service: SubscriptionConfigurationService;
	let httpMock: HttpTestingController;

	const mockPagedResponse: PaginationResponse<SubscriptionConfigurationListObj> = {
		total: 25,
		pageNum: 1,
		rows: [
			{
				topic: '111',
				topicType: 'Log',
				subscriptionType: '1222',
				orgId: '',
				id: '',
				subscriptionEventType: '',
				description: '',
				expiresAt: '',
				userId: '',
				subscriberId: '',
				createAt: '',
				subscriptionRequestUri: '',
			},
			{
				topic: '222',
				topicType: 'Log',
				subscriptionType: '1222',
				orgId: '',
				id: '',
				subscriptionEventType: '',
				description: '',
				expiresAt: '',
				userId: '',
				subscriberId: '',
				createAt: '',
				subscriptionRequestUri: '',
			},
		],
	};
	const allData = [
		{
			topic: '111',
			topicType: 'Log',
			subscriptionType: '1222',
			orgId: '',
			id: '',
			subscriptionEventType: '',
			description: '',
			expiresAt: '',
			userId: '',
			subscriberId: '',
			createAt: '',
			subscriptionRequestUri: '',
		},
	];

	const detailData: SubscriptionConfigDetailObj = {
		topic: '111',
		topicType: 'Log',
		subscriptionType: '1222',
		subscriptionEventType: [],
		description: '',
		expiresAt: '',
		userId: '',
		subscriberId: '',
		createAt: '',
	};

	const editDetailData: SubscriptionConfigDetailObj = {
		id: '111',
		topic: 'eee',
		topicType: 'eeeee',
		subscriptionType: 'wwwww',
		subscriptionEventType: [],
		description: '',
		expiresAt: '',
		userId: '',
		subscriberId: '',
		createAt: '',
	};
	const detailDataRes: SubscriptionConfigurationListObj = {
		id: '',
		topic: '111',
		topicType: 'Log',
		subscriptionType: '1222',
		subscriptionEventType: '',
		description: '',
		expiresAt: '',
		userId: '',
		orgId: '',
		subscriberId: '',
		createAt: '',
		subscriptionRequestUri: '',
	};

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [SubscriptionConfigurationService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(SubscriptionConfigurationService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should getDataPerPaget and return SubscriptionConfigurationListObj[]', () => {
		const param = { description: 'all' };

		service.getDataPerPage(param).subscribe((data) => {
			expect(data).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(param);
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(mockPagedResponse);
	});

	it('should loadData return SubscriptionListObj[]', () => {
		const param = { description: 'all' };

		service.loadAllData(param).subscribe((data) => {
			expect(data).toEqual(allData);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(param);
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(allData);
	});

	it('should saveConfiguration for create and return  SubscriptionListObj', () => {
		service.saveConfiguration(detailData).subscribe((data) => {
			expect(data).toEqual(detailDataRes);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
		expect(req.request.method).toBe('POST');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(detailDataRes);
	});

	it('should saveConfiguration for update and return  SubscriptionListObj', () => {
		service.saveConfiguration(editDetailData).subscribe((data) => {
			expect(data).toEqual(detailDataRes);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
		expect(req.request.method).toBe('PUT');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(detailDataRes);
	});

	it('should getTopicTypeOptions and return  DictList', () => {
		const types: DictObj[] = [{ code: '111', name: '111' }];
		service.getTopicTypeOptions().subscribe((data) => {
			expect(data).toEqual(types);
		});

		const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/topicType`);
		expect(req.request.method).toBe('GET');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(types);
	});

	it('should getTopicOptions and return  DictList', () => {
		const topics: string[] = ['111'];
		service.getTopicOptions().subscribe((data) => {
			expect(data).toEqual(topics);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/topic-type-list`);
		expect(req.request.method).toBe('GET');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(topics);
	});

	it('should getEventTypes and return  DictList', () => {
		const types: DictObj[] = [{ code: '111', name: '111' }];
		service.getEventTypeOptions().subscribe((data) => {
			expect(data).toEqual(types);
		});

		const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/subscriptionEventType`);
		expect(req.request.method).toBe('GET');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(types);
	});

	it('should inviteSubscriber and return  DictList', () => {
		service.inviteToSubscribe('111', 'Log', ['11', '22']).subscribe();

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/invite`);
		expect(req.request.method).toBe('POST');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');
	});
});
