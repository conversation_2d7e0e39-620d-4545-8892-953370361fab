import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { NotificationListObj } from 'src/app/modules/notification/models/notification.model';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationComponent } from '../../components/notification/notification.component';
import { Router, RouterModule } from '@angular/router';

@Component({
	selector: 'orll-notification-popup',
	imports: [MatIconModule, MatSlideToggleModule, TranslateModule, NotificationComponent, RouterModule],
	templateUrl: './notification-popup.component.html',
	styleUrl: './notification-popup.component.scss',
})
export class NotificationPopupComponent {
	notifications: NotificationListObj[] = [];

	constructor(private readonly router: Router) {}

	goToNotification() {
		this.router.navigate(['/notification']);
	}
}
