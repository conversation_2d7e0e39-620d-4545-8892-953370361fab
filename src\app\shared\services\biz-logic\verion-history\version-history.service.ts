import { Injectable } from '@angular/core';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { Observable, of } from 'rxjs';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { RequestStatusChangeAction, VersionHistoryDetailObj, VersionHistoryObj } from '@shared/models/biz-logic/version-history.model';

@Injectable({
	providedIn: 'root',
})
export class VersionHistoryService extends ApiService implements GenericTableService<VersionHistoryObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	loadAllData(param: { loId: string; type: string }): Observable<VersionHistoryObj[]> {
		if (!param.loId) {
			return of([]);
		}
		return this.getData('action-request-management/get-lo-history-action-request', param);
	}

	getDataPerPage(param: PaginationRequest): Observable<PaginationResponse<VersionHistoryObj>> {
		return this.getData<PaginationResponse<VersionHistoryObj>>('action-request-management/get-lo-history-action-request', param);
	}

	getVersionHistoryDetail(actionRequestUri: string): Observable<VersionHistoryDetailObj> {
		return this.getData('action-request-management/get-action-request-details', { actionRequestUri });
	}

	updateRequestStatus(actionRequestUri: string, status: RequestStatusChangeAction): Observable<boolean> {
		return this.updateDataPatch('action-request-management/update-action-request', { actionRequestUri, status });
	}
}
