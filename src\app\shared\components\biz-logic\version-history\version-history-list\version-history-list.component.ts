import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { OrllTableComponent } from '@shared/components/orll-table/orll-table.component';
import { OrllColumnDef } from '@shared/models/orlll-common-table';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { RequestStatus, VersionHistoryObj } from '@shared/models/biz-logic/version-history.model';
import { VersionHistoryDetailComponent } from '../version-history-detail/version-history-detail.component';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';

@Component({
	selector: 'orll-version-history-list',
	imports: [OrllTableComponent],
	templateUrl: './version-history-list.component.html',
	styleUrl: './version-history-list.component.scss',
})
export default class VersionHistoryListComponent implements OnChanges {
	@Input() loId!: string;
	@Input() type!: string;

	param: { loId: string; type: string } | null = null;

	columns: OrllColumnDef<VersionHistoryObj>[] = [
		{
			key: 'loType',
			header: 'common.change.request.object.type',
		},
		{
			key: 'actionRequestUri',
			header: 'common.change.request.object.url',
		},
		{
			key: 'version',
			header: 'common.change.request.version',
		},
		{
			key: 'actionRequestDate',
			header: 'common.change.request.date',
		},
		{
			key: 'updateOrgName',
			header: 'common.change.request.changed.by',
		},
		{
			key: 'requestStatus',
			header: 'common.change.request.status',
			transform: (val: any) => this.transformStatus(val),
			clickCell: (row: VersionHistoryObj) => this.openVersionHistory(row),
		},
	];

	constructor(
		public readonly versionService: VersionHistoryService,
		private readonly verdionDialog: MatDialog,
		private readonly translateService: TranslateService,
		private readonly cdr: ChangeDetectorRef
	) {}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['loId'] || changes['type']) {
			this.param = { loId: this.loId, type: this.type };
		}
	}

	openVersionHistory(row: VersionHistoryObj) {
		const dialogRef = this.verdionDialog.open(VersionHistoryDetailComponent, {
			width: '80vw',
			autoFocus: false,
			data: {
				title: this.translateService.instant('common.change.request.dialog.title'),
				actionRequestUri: row.actionRequestUri,
			},
		});
		dialogRef.afterClosed().subscribe((res) => {
			//refresh the dat
			if (res) {
				this.param = { loId: this.param?.loId ?? '', type: this.param?.type ?? '' };
				this.cdr.detectChanges();
			}
		});
	}

	transformStatus(val: any): string {
		return RequestStatus[val as keyof typeof RequestStatus];
	}
}
