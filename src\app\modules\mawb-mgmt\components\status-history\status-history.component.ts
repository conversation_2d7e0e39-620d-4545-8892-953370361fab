import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogTitle, MatDialogModule } from '@angular/material/dialog';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { CommonModule } from '@angular/common';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatSortModule } from '@angular/material/sort';
import { HistoryDialogData, StatusHistory } from '../../models/mawb-event.model';
import { MawbStatusService } from '../../services/mawb-status.service';
import { TranslateModule } from '@ngx-translate/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';

@Component({
	selector: 'orll-status-history',
	imports: [
		MatDialogTitle,
		MatDialogContent,
		MatTableModule,
		CommonModule,
		CommonModule,
		MatIcon,
		MatSortModule,
		TranslateModule,
		MatDialogModule,
		MatIconModule,
		SpinnerComponent,
	],
	templateUrl: './status-history.component.html',
	styleUrl: './status-history.component.scss',
})
export class StatusHistoryComponent implements OnInit {
	dataLoading = false;
	readonly displayedColumns: string[] = ['event', 'updateTime', 'updateBy', 'eventTimeType', 'partialEvent'];
	dataSource = new MatTableDataSource<StatusHistory>();

	constructor(
		private readonly statusService: MawbStatusService,
		@Inject(MAT_DIALOG_DATA) public data: HistoryDialogData
	) {}

	ngOnInit(): void {
		this.initData();
	}

	private initData(): void {
		this.dataLoading = true;

		this.statusService.getStatusHistoryList(this.data.loId, this.data.type).subscribe({
			next: (items: StatusHistory[]) => {
				this.dataSource.data = items;
				this.dataLoading = false;
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}
}
