import { ChangeDetector<PERSON>ef, Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MAT_DIALOG_DATA, MatDialog, MatDialogContent, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatOption } from '@angular/material/autocomplete';
import { MatSelect } from '@angular/material/select';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { ConsolidatePieceDialogData, PieceList } from '../../models/piece/piece-list.model';
import { CodeName } from '@shared/models/code-name.model';
import { PieceType } from '../../models/piece/piece-type.model';
import { deepCopy, displayPackagingTypeName } from '@shared/utils/common.utils';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { REGX_NUMBER_1_DECIMAL } from '@shared/models/constant';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { Piece } from '../../models/piece/piece.model';
import { MatFormField, MatInputModule } from '@angular/material/input';
import { ConfirmDialogDirective } from '@shared/directive/confirm-dialog.directive';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';

@Component({
	selector: 'orll-piece-consolidate-dialog',
	imports: [
		ReactiveFormsModule,
		FormsModule,
		MatDialogContent,
		TranslateModule,
		MatTableModule,
		MatIconModule,
		TranslateModule,
		MatTableModule,
		MatSortModule,
		MatDialogModule,
		MatCheckboxModule,
		MatButtonModule,
		MatInputModule,
		MatFormField,
		MatOption,
		MatSelect,
		SpinnerComponent,
		ConfirmDialogDirective,
	],
	templateUrl: './piece-consolidate-dialog.component.html',
	styleUrl: './piece-consolidate-dialog.component.scss',
})
export class PieceConsolidateDialogComponent extends DestroyRefComponent implements OnInit {
	displayedColumns: string[] = ['pieceDescription', 'packagingType', 'grossWeight', 'dimensions', 'pieceQuantity', 'delete'];
	packagingTypes: CodeName[] = [];
	dataLoading = false;

	dataSource = new MatTableDataSource<PieceList>([]);

	pieceInfoForm: FormGroup = new FormGroup({
		productDescription: new FormControl<string>('', [Validators.required]),
		hsCommodityDescription: new FormControl<string>(''),
		packagingType: new FormControl<string>('', [Validators.required]),
		packagedIdentifier: new FormControl<string>(''),
		grossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		nvdForCustoms: new FormControl<string>(DropDownType.NCV),
		nvdForCarriage: new FormControl<string>(DropDownType.NVD),
		upid: new FormControl<string>(''),
		shippingMarks: new FormControl<string>(''),
		textualHandlingInstructions: new FormControl<string>(''),
	});

	constructor(
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly cdr: ChangeDetectorRef,
		private readonly dialog: MatDialog,
		private readonly dialogRef: MatDialogRef<PieceConsolidateDialogComponent>,
		private readonly translate: TranslateService,
		@Inject(MAT_DIALOG_DATA) public data: ConsolidatePieceDialogData
	) {
		super();
	}

	ngOnInit(): void {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
		});

		if (this.data.pieceId) {
			this.dataLoading = true;
			this.sliCreateRequestService
				.getPieceDetail(this.data.pieceId)
				.pipe(takeUntilDestroyed(this.destroyRef))
				.subscribe({
					next: (res) => {
						if (res) {
							this.cdr.markForCheck();
							this.fillPieceForm(res);
							this.dataSource.data = res.containedPieces
								? res.containedPieces.map((item) => ({
										type: PieceType.GENERAL,
										pieceId: item.id || '',
										productDescription: item.product.description,
										packagingType: item.packagingType.description,
										grossWeight: item.grossWeight,
										dimensions: item.dimensions,
										pieceQuantity: item.pieceQuantity,
										slac: item.slac || 0,
									}))
								: [];
							this.cdr.markForCheck();
							this.dataLoading = false;
						}
					},
				});
		} else {
			this.fillPieceFormForConsolidate(this.data.pieces);
			this.data.pieces.forEach((item) => {
				item.originalQuantity = item.pieceQuantity;
			});
			this.dataSource.data = deepCopy(this.data.pieces);
		}
	}

	deletePiece(event: MouseEvent, row: PieceList) {
		event.preventDefault();
		this.dataSource.data = this.dataSource.data.filter((item) => item.pieceId !== row.pieceId);
	}

	consolidatePiece() {
		this.pieceInfoForm.markAllAsTouched();
		const formData = this.getFormData();
		if (formData) {
			this.dataLoading = true;
			this.sliCreateRequestService.consolidatePiece(formData).subscribe({
				next: () => {
					this.dataLoading = false;
					this.dialogRef.close({ success: true });
				},
				error: () => {
					this.dataLoading = false;
					this.dialogRef.close();
				},
			});
		}
	}

	getFormData() {
		if (this.pieceInfoForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});
			return null;
		}
		return {
			piece: {
				sliNumber: this.data.sliNumber,
				type: PieceType.GENERAL,
				slac: this.dataSource.data.length,
				product: {
					description: this.pieceInfoForm.value.productDescription,
					hsCommodityDescription: this.pieceInfoForm.value.hsCommodityDescription,
				},
				packagingType: {
					typeCode: this.pieceInfoForm.value.packagingType,
					description: displayPackagingTypeName(this.packagingTypes, this.pieceInfoForm.value.packagingType),
				},
				packagedIdentifier: this.pieceInfoForm.value.packagedIdentifier,
				grossWeight: this.pieceInfoForm.value.grossWeight ? Number(this.pieceInfoForm.value.grossWeight) : 0,
				dimensions: {
					length: this.pieceInfoForm.value.dimLength ? Number(this.pieceInfoForm.value.dimLength) : 0,
					width: this.pieceInfoForm.value.dimWidth ? Number(this.pieceInfoForm.value.dimWidth) : 0,
					height: this.pieceInfoForm.value.dimHeight ? Number(this.pieceInfoForm.value.dimHeight) : 0,
				},
				nvdForCustoms: this.pieceInfoForm.value.nvdForCustoms,
				nvdForCarriage: this.pieceInfoForm.value.nvdForCarriage,
				upid: this.pieceInfoForm.value.upid,
				shippingMarks: this.pieceInfoForm.value.shippingMarks,
				textualHandlingInstructions: this.pieceInfoForm.value.textualHandlingInstructions,
				pieceQuantity: this.dataSource.data.reduce((sum, item) => sum + item.pieceQuantity || 0, 0),
				containedItems: [],
			},
			originalPieces: this.dataSource.data,
		};
	}

	fillPieceForm(piece: Piece): void {
		this.pieceInfoForm.patchValue({
			productDescription: piece?.product.description ?? '',
			hsCommodityDescription: piece?.product.hsCommodityDescription ?? '',
			packagingType: piece?.packagingType.typeCode ?? '',
			packagedIdentifier: piece?.packagedIdentifier ?? '',
			grossWeight: piece?.grossWeight ?? '',
			dimLength: piece?.dimensions.length ?? '',
			dimWidth: piece?.dimensions.width ?? '',
			dimHeight: piece?.dimensions.height ?? '',
			nvdForCustoms: piece?.nvdForCustoms,
			nvdForCarriage: piece.nvdForCarriage,
			upid: piece?.upid ?? '',
			shippingMarks: piece?.shippingMarks ?? '',
			textualHandlingInstructions: piece?.textualHandlingInstructions ?? '',
		});
	}

	fillPieceFormForConsolidate(pieces: PieceList[]) {
		this.pieceInfoForm.patchValue({
			productDescription: pieces.map((item) => item.productDescription ?? '').join(' '),
			grossWeight: pieces.reduce((sum, item) => {
				const weight = item.grossWeight ?? 0;
				const quantity = item.pieceQuantity ?? 0;
				return sum + weight * quantity;
			}, 0),
			textualHandlingInstructions: pieces.map((item) => item.textualHandlingInstructions ?? '').join(' '),
			nvdForCustoms: true,
			nvdForCarriage: true,
		});
	}

	calculateGrossWeight(event: Event, row: PieceList) {
		const input = event.target as HTMLInputElement;
		const value = input.value.trim();
		if (!value) {
			row.pieceQuantity = row.originalQuantity || 1;
		}
		let quantity = 1;
		if (value && !isNaN(Number(value))) {
			quantity = Number(value);
			if (row.originalQuantity && quantity > row.originalQuantity) {
				row.pieceQuantity = row.originalQuantity;
				quantity = row.originalQuantity;
			}
			//calculate weight of current pieces
			const currentPieceGrossWeight = parseFloat((quantity * (row.grossWeight ?? 0)).toFixed(2));
			//calcualte total weight of other pieces
			const othePiecesGrossWeight = this.data.pieces
				.filter((item) => item.pieceId !== row.pieceId)
				.reduce((sum, item) => sum + (item.grossWeight ?? 0), 0);
			this.pieceInfoForm.patchValue({ grossWeight: currentPieceGrossWeight + othePiecesGrossWeight });
		}
	}

	cancelConsolidate() {
		this.dialogRef.close();
	}
}
