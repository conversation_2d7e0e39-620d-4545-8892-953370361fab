import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatDialog } from '@angular/material/dialog';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { DelegationRequest } from '@shared/models/delegation-request';
import { DelegationRequestDialogComponent } from '../delegation-request-dialog/delegation-request-dialog.component';
import { DelegationRequestService } from '@shared/services/delegation/delegation-request.service';

@Component({
	selector: 'orll-delegation-request-table',
	templateUrl: './delegation-request-table.component.html',
	styleUrl: './delegation-request-table.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTableModule, MatSortModule, MatButtonModule, MatIconModule, MatPaginatorModule, TranslateModule, SpinnerComponent],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class DelegationRequestTableComponent extends DestroyRefComponent implements OnChanges {
	@Input() records: DelegationRequest[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;
	@Input() enableInfiniteScroll = false;
	@Input() hasMoreData = true;
	@Input() isCentralMenu = false;

	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();
	@Output() loadMoreData = new EventEmitter<void>();

	currentSort: Sort = { active: '', direction: '' };

	constructor(
		private readonly dialog: MatDialog,
		private readonly delegationRequestService: DelegationRequestService
	) {
		super();
	}

	readonly displayedColumns: string[] = ['hasDescription', 'isRequestedFor', 'isRequestedBy', 'isRequestedAt', 'requestStatus'];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<DelegationRequest>(this.records || []);
	dataLoading = false;

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
	}

	onSortChange(sort: Sort): void {
		this.currentSort = sort;
		this.sortChange.emit(sort);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	trackByLoId(record: DelegationRequest): string {
		return record.id ?? '';
	}

	onTableScroll(event: Event): void {
		const container = event.target as HTMLDivElement;
		const scrollTop = container.scrollTop;
		const scrollHeight = container.scrollHeight;
		const clientHeight = container.clientHeight;

		const isAtBottom = scrollHeight - (scrollTop + clientHeight) === 0;

		if (this.enableInfiniteScroll && isAtBottom && !this.dataLoading && this.hasMoreData) {
			this.loadMoreData.emit();
		}
	}

	openDelegationRequestDetail(record: DelegationRequest) {
		this.delegationRequestService.getDelegationDetail(record.requestId ?? '').subscribe((res) => {
			this.dialog.open(DelegationRequestDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					record: res,
					isApprovedBy: record.isApprovedBy,
				},
			});
		});
	}

	createDelegationRequest() {
		this.dialog.open(DelegationRequestDialogComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				icon: 'send',
				ok: 'common.dialog.delegation.request.send',
			},
		});
	}
}
