import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PieceConsolidateDialogComponent } from './piece-consolidate-dialog.component';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { ConsolidatePieceDialogData, PieceList } from '../../models/piece/piece-list.model';
import { REGX_NUMBER_1_DECIMAL } from '@shared/models/constant';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';

describe('PieceConsolidateDialogComponent', () => {
	let component: PieceConsolidateDialogComponent;
	let fixture: ComponentFixture<PieceConsolidateDialogComponent>;
	let sliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<any>>;

	const mockDialogData: ConsolidatePieceDialogData = {
		pieces: [],
		sliNumber: '1111',
		pieceId: '',
	};

	beforeEach(() => {
		const sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['consolidatePiece']);
		const matDialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

		TestBed.configureTestingModule({
			imports: [PieceConsolidateDialogComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: MatDialogRef, useValue: matDialogRefSpy },
				FormBuilder,

				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },

				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		fixture = TestBed.createComponent(PieceConsolidateDialogComponent);
		component = fixture.componentInstance;
		sliCreateRequestService = TestBed.inject(SliCreateRequestService) as jasmine.SpyObj<SliCreateRequestService>;
		dialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<any>>;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should delete a piece when deletePiece is called', () => {
		const event = new Event('click') as any;
		event.preventDefault = jasmine.createSpy('preventDefault');

		const pieceIdToDelete = '123';

		const pieces: PieceList[] = [
			{
				pieceId: pieceIdToDelete,
				type: '123',
				productDescription: '',
				packagingType: '',
				grossWeight: 0,
				dimensions: { length: 0, width: 0, height: 0 },
				pieceQuantity: 0,
				slac: 0,
			},
			{
				pieceId: '456',
				type: '',
				productDescription: '',
				packagingType: '',
				grossWeight: 0,
				dimensions: { length: 0, width: 0, height: 0 },
				pieceQuantity: 0,
				slac: 0,
			},
		];

		component.dataSource.data = pieces;

		component.deletePiece(event, { pieceId: pieceIdToDelete } as any);

		expect(event.preventDefault).toHaveBeenCalled();
		expect(component.dataSource.data.length).toBe(1);
		expect(component.dataSource.data[0].pieceId).not.toBe(pieceIdToDelete);
	});

	it('should call consolidatePiece and close dialog on success', () => {
		const formData = {
			piece: {
				sliNumber: 'pieceId1',
				type: 'Piece',
				product: { description: '44', hsCommodityDescription: '4' },
				packagingType: { typeCode: '1A', description: 'Drum, steel' },
				packagedIdentifier: '4',
				grossWeight: 4,
				dimensions: { length: 4, width: 4, height: 4 },
				nvdForCustoms: false,
				nvdForCarriage: false,
				upid: '4',
				shippingMarks: '44',
				textualHandlingInstructions: '444',
				pieceQuantity: 0,
				containedItems: [],
			},
			originalPieces: [
				{
					pieceId: 'pieceId1',
					productDescription: '666',
					packagingType: 'Drum, aluminium',
					grossWeight: 6,
					dimensions: { id: null, length: 6, width: 6, height: 6 },
					pieceQuantity: 1,
					slac: 0,
					type: 'Piece',
				},
				{
					pieceId: 'subPiece1',
					productDescription: '888',
					packagingType: 'Drum, plywood',
					grossWeight: 8,
					dimensions: { id: null, length: 8, width: 8, height: 8 },
					pieceQuantity: 1,
					slac: 0,
					type: 'Piece',
				},
			],
		};
		component.dataLoading = false;
		component.pieceInfoForm = new FormGroup({
			productDescription: new FormControl<string>('', [Validators.required]),
			hsCommodityDescription: new FormControl<string>(''),
			packagingType: new FormControl<string>('', [Validators.required]),
			packagedIdentifier: new FormControl<string>(''),
			grossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
			nvdForCustoms: new FormControl<string>(DropDownType.NCV),
			nvdForCarriage: new FormControl<string>(DropDownType.NVD),
			upid: new FormControl<string>(''),
			shippingMarks: new FormControl<string>(''),
			textualHandlingInstructions: new FormControl<string>(''),
		});
		component.getFormData = jasmine.createSpy('getFormData').and.returnValue(formData);

		sliCreateRequestService.consolidatePiece.and.returnValue(of(true));

		component.consolidatePiece();

		expect(component.dataLoading).toBe(false);
		expect(component.getFormData).toHaveBeenCalled();
		expect(sliCreateRequestService.consolidatePiece).toHaveBeenCalledWith(formData);
		expect(component.dataLoading).toBe(false);
		expect(dialogRef.close).toHaveBeenCalled();
	});

	it('should handle error in consolidatePiece', () => {
		component.dataLoading = false;
		component.pieceInfoForm = new FormGroup({});
		component.getFormData = jasmine.createSpy('getFormData').and.returnValue(null);

		component.consolidatePiece();

		expect(component.dataLoading).toBe(false);
	});
});
