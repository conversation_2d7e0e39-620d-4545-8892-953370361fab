<div class="orll-delegation-request-dialog">
	<h2 mat-dialog-title class="orll-delegation-request-dialog__title">
		{{ data.title || 'common.dialog.delegation.request.title' | translate }}
	</h2>
	<mat-dialog-content class="orll-delegation-request-dialog__content">
		@if (!delegationDetail) {
			<form [formGroup]="delegationRequestForm">
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'common.dialog.delegation.request.description' | translate }}</mat-label>
						<textarea rows="3" matInput formControlName="hasDescription"></textarea>
						@if (delegationRequestForm.get('hasDescription')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'common.dialog.delegation.request.description' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row padding-lr-15">
					<iata-autocomplete
						#orglist
						[id]="'orglist'"
						[api]="sliSearchRequestService"
						[label]="'common.dialog.delegation.request.for' | translate"
						[multiple]="true"
						(selected)="selectedOrgs = $event">
					</iata-autocomplete>
				</div>
				<div class="row padding-lr-15">
					<mat-label class="label margin-t-8">{{ 'common.dialog.delegation.request.permissions' | translate }}*: </mat-label>
					<form [formGroup]="delegationRequestForm.get('hasPermission')" class="checkbox-form">
						@for (permission of delegationPermissionList; track permission.code) {
							<mat-checkbox [formControlName]="permission.code">{{ permission.name | translate }}</mat-checkbox>
						}
						@if (submitted && delegationRequestForm.get('hasPermission')?.hasError('atLeastOneRequired')) {
							<mat-error class="error-msg">{{
								'validators.required' | translate: { field: 'common.dialog.delegation.request.permissions' | translate }
							}}</mat-error>
						}
					</form>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'common.dialog.delegation.request.logisticsObject' | translate }}</mat-label>
						<textarea rows="3" matInput formControlName="hasLogisticsObject"></textarea>
						@if (delegationRequestForm.get('hasLogisticsObject')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'common.dialog.delegation.request.logisticsObject' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
			</form>
		} @else {
			<div class="row col-12">
				<mat-label class="label">{{ 'common.dialog.delegation.request.description' | translate }}:&nbsp;&nbsp;</mat-label>
				<span>{{ delegationDetail.hasDescription }}</span>
			</div>
			<div class="row">
				<div class="col-6">
					<mat-label class="label">{{ 'common.dialog.delegation.request.for' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ delegationDetail.isRequestedFor?.join(',') }}</span>
				</div>
				<div class="col-6">
					<mat-label class="label">{{ 'common.dialog.delegation.request.by' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ delegationDetail.isRequestedBy }}</span>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<mat-label class="label">{{ 'common.dialog.delegation.request.status' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ delegationDetail.requestStatus }}</span>
				</div>
				<div class="col-6">
					<mat-label class="label">{{ 'common.dialog.delegation.request.at' | translate }}:&nbsp;&nbsp;</mat-label>
					<span>{{ delegationDetail.isRequestedAt }}</span>
				</div>
			</div>
			<div class="row col-12">
				<mat-label class="label">{{ 'common.dialog.delegation.request.permissions' | translate }}:&nbsp;&nbsp;</mat-label>
				@for (permission of delegationDetail.hasPermission; track permission) {
					<mat-icon class="margin-l-10 icon">check_circle</mat-icon>
					<span class="margin-l-10">{{ permission }}</span>
				}
			</div>
			<div class="row col-12">
				<mat-label class="label">{{ 'common.dialog.delegation.request.logisticsObject' | translate }}:&nbsp;&nbsp;</mat-label>
				<span>{{ delegationDetail.hasLogisticsObject?.join(',') }}</span>
			</div>
		}
	</mat-dialog-content>
	<mat-dialog-actions align="end">
		<div class="orll-delegation-request-dialog__actions row col-12">
			<div class="col-8 revoke">
				@if (requestStatus === delegationRequestStatus.PENDING || requestStatus === delegationRequestStatus.APPROVED) {
					<button mat-stroked-button color="primary" (click)="onRevoke()" class="orll-delegation-request-dialog__revoke-button">
						<mat-icon>u_turn_left</mat-icon>
						{{ 'common.dialog.revoke' | translate }}
					</button>
				}
			</div>
			<div class="col-4">
				@if (isApprovedBy && requestStatus === delegationRequestStatus.PENDING) {
					<button mat-stroked-button color="primary" (click)="onReject()" class="orll-delegation-request-dialog__reject-button">
						{{ 'common.dialog.reject' | translate }}
					</button>
					<button mat-flat-button color="primary" (click)="onApprove()" class="orll-delegation-request-dialog__approve-button">
						<mat-icon>check_circle</mat-icon>
						{{ 'common.dialog.approve' | translate }}
					</button>
				}

				<button mat-stroked-button color="primary" (click)="onCancel()" class="orll-delegation-request-dialog__cancel-button">
					{{ 'common.dialog.cancel' | translate }}
				</button>
				@if (!delegationDetail) {
					<button mat-flat-button color="primary" (click)="onOk()" class="orll-delegation-request-dialog__ok-button">
						<mat-icon>{{ data.icon || 'check' }}</mat-icon>
						{{ data.ok || 'common.dialog.ok' | translate }}
					</button>
				}
			</div>
		</div>
	</mat-dialog-actions>
</div>
