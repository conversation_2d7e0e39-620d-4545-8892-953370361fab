import { TranslateModule } from '@ngx-translate/core';
import { Component, OnInit } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { DictObj } from '../../models/subscription.model';
import { Organization } from '@shared/models/organization.model';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';

@Component({
	selector: 'orll-invite-subscribe',
	imports: [
		MatDialogModule,
		MatIconModule,
		MatSelectModule,
		ReactiveFormsModule,
		MatDividerModule,
		MatButtonModule,
		TranslateModule,
		MatInputModule,
		CommonModule,
	],
	templateUrl: './invite-subscribe.component.html',
	styleUrl: './invite-subscribe.component.scss',
})
export class InviteSubscribeComponent implements OnInit {
	topicTypes: DictObj[] = [];
	topics: string[] = [];
	partners: Organization[] = [];
	inviteForm: FormGroup = new FormGroup({
		topicType: new FormControl<string>('', [Validators.required]),
		topic: new FormControl<string>('', [Validators.required]),
		subscribers: new FormControl<string[]>([], [Validators.required]),
	});
	dataLoading = false;

	constructor(
		private readonly subscriptionService: SubscriptionConfigurationService,
		private readonly orgService: OrgMgmtRequestService,
		private readonly dialogRef: MatDialogRef<InviteSubscribeComponent>
	) {}

	ngOnInit(): void {
		this.loadData();
	}

	loadData() {
		this.subscriptionService.getTopicTypeOptions().subscribe((res) => {
			this.topicTypes = res;
		});
		this.subscriptionService.getTopicOptions().subscribe((res) => {
			this.topics = res;
		});
		this.orgService.getOrgList().subscribe((res) => {
			this.partners = res;
		});
	}

	inviteToSubscribe() {
		if (this.inviteForm.invalid) {
			this.inviteForm.markAllAsTouched();
			return;
		}

		this.subscriptionService
			.inviteToSubscribe(
				this.inviteForm.get('topic')!.value,
				this.inviteForm.get('topicType')!.value,
				this.inviteForm.get('subscribers')!.value
			)
			.subscribe({
				next: () => {
					this.dataLoading = false;
					this.dialogRef.close();
				},
				error: () => {
					this.dataLoading = false;
					this.dialogRef.close();
				},
			});
	}
}
