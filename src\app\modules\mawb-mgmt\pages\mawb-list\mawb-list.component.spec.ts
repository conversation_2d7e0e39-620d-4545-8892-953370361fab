import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import { default as MawbListComponent } from './mawb-list.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideTranslateService, TranslateModule } from '@ngx-translate/core';
import { MawbSearchRequestService } from '../../services/mawb-search-request.service';
import { MawbListObject } from '../../models/mawb-list-object.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MawbSearchPayload } from '../../models/mawb-search-payload.model';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { of, throwError } from 'rxjs';
import { UserProfileService } from '@shared/services/user-profile.service';

describe('MawbListComponent', () => {
	let component: MawbListComponent;
	let fixture: ComponentFixture<MawbListComponent>;
	let searchService: MawbSearchRequestService;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	const mockMawbList: MawbListObject[] = [
		{
			mawbId: '1',
			mawbNumber: '123-456789',
			airlineCode: 'AA',
			goodsDescription: 'Electronics',
			origin: 'JFK',
			destination: 'LAX',
			pieceQuantity: '10',
			latestStatus: 'Created',
			createDate: '2023-01-01',
			orgId: 'org1',
			eventDate: '',
		},
	];

	const mockPageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	const mockSearchPayload: MawbSearchPayload = {
		goodsDescription: 'Electronics',
		airlineCodeList: ['AA'],
	};

	beforeEach(async () => {
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		const searchServiceMock = {
			getMawbList: jasmine.createSpy('getMawbList').and.returnValue(
				of({
					rows: mockMawbList,
					total: 1,
				})
			),
			getOptions: jasmine.createSpy('getOptions'),
		};

		await TestBed.configureTestingModule({
			imports: [MawbListComponent],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				{ provide: MawbSearchRequestService, useValue: searchServiceMock },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbListComponent);
		component = fixture.componentInstance;
		searchService = TestBed.inject(MawbSearchRequestService);
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should load MAWB list on ngOnInit', fakeAsync(() => {
		component.ngOnInit();
		tick();

		expect(searchService.getMawbList).toHaveBeenCalledWith(
			jasmine.objectContaining({
				pageNum: 1,
				pageSize: 10,
			}),
			undefined as any
		);
		expect(component.mawbList).toEqual(mockMawbList);
		expect(component.mawbListTotalRecords).toBe(1);
		expect(component.dataLoading).toBeFalse();
	}));

	it('should handle search events', fakeAsync(() => {
		component.onSearch(mockSearchPayload);
		tick();

		expect(component.mawbSearchPayload).toEqual(mockSearchPayload);
		expect(searchService.getMawbList).toHaveBeenCalledWith(
			jasmine.objectContaining({
				pageNum: 1,
				pageSize: 10,
			}),
			mockSearchPayload
		);
		expect(component.mawbList).toEqual(mockMawbList);
	}));

	it('should handle sort changes', () => {
		const sortEvent: Sort = { active: 'createDate', direction: 'desc' };

		component.onMawbListSortChange(sortEvent);

		expect(component.pageParams.orderByColumn).toBe('createDate');
		expect(component.pageParams.isAsc).toBe('desc');
	});

	it('should clear sort parameters when direction is empty', () => {
		const sortEvent: Sort = { active: 'createDate', direction: '' };

		component.onMawbListSortChange(sortEvent);

		expect(component.pageParams.orderByColumn).toBe('');
		expect(component.pageParams.isAsc).toBe('');
	});

	it('should handle pagination events', fakeAsync(() => {
		const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
			pageIndex: 2,
			pageSize: 50,
			length: 100,
			sortField: 'mawbNumber',
			sortDirection: 'asc',
		};

		component.onMawbListPageChange(pageEvent);
		tick();

		expect(component.pageParams.pageNum).toBe(3);
		expect(component.pageParams.pageSize).toBe(50);
		expect(component.pageParams.orderByColumn).toBe('mawbNumber');
		expect(component.pageParams.isAsc).toBe('asc');
		expect(searchService.getMawbList).toHaveBeenCalled();
	}));

	it('should handle pagination without sort info', fakeAsync(() => {
		const pageEvent: PageEvent = {
			pageIndex: 0,
			pageSize: 20,
			length: 100,
		};

		component.onMawbListPageChange(pageEvent);
		tick();

		expect(component.pageParams.pageNum).toBe(1);
		expect(component.pageParams.pageSize).toBe(20);
		expect(searchService.getMawbList).toHaveBeenCalled();
	}));

	it('should handle service errors', fakeAsync(() => {
		const errorServiceMock = {
			getMawbList: jasmine.createSpy('getMawbList').and.returnValue(throwError(() => new Error('Test error'))),
		};

		TestBed.resetTestingModule();
		TestBed.configureTestingModule({
			imports: [MawbListComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbSearchRequestService, useValue: errorServiceMock },
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		const errorFixture = TestBed.createComponent(MawbListComponent);
		const errorComponent = errorFixture.componentInstance;

		// @ts-expect-error private
		errorComponent.getMawbListPage(mockPageParams);
		tick();

		expect(errorComponent.dataLoading).toBeFalse();
		expect(errorComponent.mawbList).toEqual([]);
	}));

	it('should pass correct parameters to service', fakeAsync(() => {
		const complexParams: PaginationRequest = {
			pageNum: 2,
			pageSize: 50,
			orderByColumn: 'createDate',
			isAsc: 'desc',
		};

		// @ts-expect-error private
		component.getMawbListPage(complexParams);
		tick();

		expect(searchService.getMawbList).toHaveBeenCalledWith(
			jasmine.objectContaining({
				pageNum: 2,
				pageSize: 50,
				orderByColumn: 'createDate',
				isAsc: 'desc',
			}),
			undefined as any
		);
	}));

	it('should reset page number on new search', fakeAsync(() => {
		// Set to page 3
		component.pageParams.pageNum = 3;

		component.onSearch(mockSearchPayload);
		tick();

		expect(component.pageParams.pageNum).toBe(3);
	}));
});
