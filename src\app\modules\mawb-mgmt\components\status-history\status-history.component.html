<div class="orll-status-history">
	<h2 mat-dialog-title>
		<span>{{ 'mawb.event.status.history' | translate }}</span
		><mat-icon class="orll-status-history__clear_dialog" [matDialogClose]="'cancel'">clear_round</mat-icon>
	</h2>
	<mat-dialog-content class="iata-box">
		<table mat-table [dataSource]="dataSource" matSort class="orll-status-history__table">
			<ng-container matColumnDef="event">
				<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'mawb.event.history.table.event' | translate }}</th>
				<td mat-cell *matCellDef="let row">
					{{ row.latestStatus }}
				</td>
			</ng-container>

			<ng-container matColumnDef="updateTime">
				<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'mawb.event.history.table.time' | translate }}</th>
				<td mat-cell *matCellDef="let row">
					{{ row.eventDate }}
				</td>
			</ng-container>

			<ng-container matColumnDef="updateBy">
				<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'mawb.event.history.table.user' | translate }}</th>
				<td mat-cell *matCellDef="let row">
					{{ `${row.orgName}:${row.userName}` }}
				</td>
			</ng-container>

			<ng-container matColumnDef="eventTimeType">
				<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'mawb.event.history.table.event.time.type' | translate }}</th>
				<td mat-cell *matCellDef="let row">
					{{ row.eventTimeType }}
				</td>
			</ng-container>
			<ng-container matColumnDef="partialEvent">
				<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'mawb.event.history.table.partial.event' | translate }}</th>
				<td mat-cell *matCellDef="let row">
					@if (row.partialEventIndicator) {
						<mat-icon [style.color]="'green'">check</mat-icon>
					}
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
		</table>
	</mat-dialog-content>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
