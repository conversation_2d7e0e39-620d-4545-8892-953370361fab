import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	QueryList,
	ViewChild,
	ViewChildren,
} from '@angular/core';
import { AsyncPipe, DatePipe } from '@angular/common';
import { ShipperOrConsigneeInfoComponent } from './shipper-or-consignee-info/shipper-or-consignee-info.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CarrierAgentComponent } from './carrier-agent/carrier-agent.component';
import { IssuedByComponent } from './issued-by/issued-by.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import {
	FormControl,
	FormGroup,
	NonNullableFormBuilder,
	ReactiveFormsModule,
	Validators,
	AbstractControl,
	ValidationErrors,
} from '@angular/forms';
import { AirportInfoComponent } from './airport-info/airport-info.component';
import { MatTableModule } from '@angular/material/table';
import { OtherChargesComponent } from './other-charges/other-charges.component';
import { SliConsigneeComponent } from '../../../sli-mgmt/components/sli-consignee/sli-consignee.component';
import { ShipmentParty } from '../../../sli-mgmt/models/shipment-party.model';
import { PrepaidCollectComponent } from './prepaid-collect/prepaid-collect.component';
import { combineLatest, forkJoin, of, startWith, tap } from 'rxjs';
import { SliCreateRequestService } from '../../../sli-mgmt/services/sli-create-request.service';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { Person } from '@shared/models/person.model';
import { OrgType } from '@shared/models/org-type.model';
import { MatDialog } from '@angular/material/dialog';
import { MawbCreateRequestService } from '../../services/mawb-create-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { MawbCreateDto, OtherChargeList, PartyList } from '../../models/mawb-create.model';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatOptionModule, provideNativeDateAdapter } from '@angular/material/core';
import { EnumCodeFormItemComponent } from '@shared/components/enum-code-form-item/enum-code-form-item.component';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Router } from '@angular/router';
import { isBlank } from '@shared/utils/type.utils';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { EmitCharges } from '../../models/other-charges.model';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { Organization } from '@shared/models/organization.model';
import { OrgInfo } from '@shared/models/org-info.model';
import { HawbListObject } from 'src/app/modules/hawb-mgmt/models/hawb-list-object.model';
import { AccountingNoteIdentifier } from '../../models/accounting-note-identifier.model';
import { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
// eslint-disable-next-line @typescript-eslint/naming-convention
import HawbListPageComponent from 'src/app/modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component';
import { MawbStatusListComponent } from '../../components/mawb-status-list/mawb-status-list.component';
import { DelegationRequestListComponent } from '@shared/components/delegation-request-list/delegation-request-list.component';
// eslint-disable-next-line @typescript-eslint/naming-convention
import VersionHistoryListComponent from '@shared/components/biz-logic/version-history/version-history-list/version-history-list.component';
import { SubscriptionRequestListComponent } from 'src/app/modules/subscription/components/subscription-request-list/subscription-request-list.component';
import { SubscriptionRequest } from 'src/app/modules/subscription/models/subscription.model';

const DATE_FORMAT = 'yyyy-MM-dd HH:mm:ss';
const REGX_NUMBER_1_DECIMAL = /^([1-9]\d*(\.\d)?|0\.[1-9])$/;
const REGX_NUMBER_2_DECIMAL = /^([1-9]\d*(\.\d{1,2})?|0\.(?!0+$)\d{1,2})$/;

@Component({
	selector: 'orll-create-mawb-from-hawb',
	imports: [
		ShipperOrConsigneeInfoComponent,
		MatButtonModule,
		MatExpansionModule,
		MatIconModule,
		MatSelectModule,
		TranslateModule,
		SliConsigneeComponent,
		CarrierAgentComponent,
		IssuedByComponent,
		MatFormFieldModule,
		MatInput,
		ReactiveFormsModule,
		AirportInfoComponent,
		MatTableModule,
		OtherChargesComponent,
		PrepaidCollectComponent,
		CurrencyInputComponent,
		MatAutocompleteModule,
		MatOptionModule,
		EnumCodeFormItemComponent,
		MatDatepickerModule,
		MatTabsModule,
		AsyncPipe,
		HawbListPageComponent,
		MawbStatusListComponent,
		DelegationRequestListComponent,
		VersionHistoryListComponent,
		SubscriptionRequestListComponent,
	],
	providers: [provideNativeDateAdapter(), DatePipe],
	changeDetection: ChangeDetectionStrategy.OnPush,
	templateUrl: './create-mawb-from-hawb.component.html',
	styleUrl: './create-mawb-from-hawb.component.scss',
})
export default class CreateMawbFromHawbComponent extends RolesAwareComponent implements OnInit, AfterViewInit {
	protected readonly enumCodeTypeModel = EnumCodeTypeModel;

	@Input() mawbId?: string;

	@ViewChildren('shipperInfoComponent,consigneeInfoComponent')
	shipperOrConsigneeInfoComponentList!: QueryList<ShipperOrConsigneeInfoComponent>;

	@ViewChild(CarrierAgentComponent)
	carrierAgentComponent!: CarrierAgentComponent;

	@ViewChild(IssuedByComponent)
	issuedByComponent!: IssuedByComponent;

	@ViewChild(AirportInfoComponent)
	airPortInfoComponent!: AirportInfoComponent;

	@ViewChild(OtherChargesComponent)
	otherChargesComponent!: OtherChargesComponent;

	@ViewChild(PrepaidCollectComponent)
	prepaidCollectComponent!: PrepaidCollectComponent;

	@ViewChild('tabGroup') tabGroup!: MatTabGroup;

	@ViewChild('delegationRequestList') delegationRequestList!: DelegationRequestListComponent;

	private orgId: string | undefined;

	alsoNotifies: ShipmentParty[] = [];
	isConfirmed = false;
	haveAccountNo = true;
	flightDisabled = false;
	hawbIdList: string[] = [];
	mawbNumber = '';
	currentTabLabel = '';

	carriers: Organization[] = [];
	carrierInfo: OrgInfo | ShipmentParty | null = null;
	issuedByInfo: OrgInfo | null = null;
	hawbList: HawbListObject[] = [];
	otherChargesList: OtherChargeList[] = [];

	mawbForm = this.fb.group({
		mawbPrefix: ['', [Validators.required]],
		mawbNumber: ['', [Validators.required, this.mawbNumberValidator.bind(this)]],
		accountingInformation: [''],
		handingInformation: [''],
		noOfPiecesRcp: new FormControl<number | null>({ value: null, disabled: true }),
		grossWeight: new FormControl<number | null>(null, [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		serviceCode: new FormControl<string | null>(null),
		rateClass: new FormControl<string | null>(null),
		chargeableWeight: new FormControl<number | null>(null, [Validators.required, Validators.pattern(/^([1-9]\d*(\.5)?|0\.5)$/)]),
		rateCharge: new FormGroup(
			{
				currencyUnit: new FormControl('', [Validators.required]),
				numericalValue: new FormControl<number | null>(null, [Validators.required, Validators.pattern(REGX_NUMBER_2_DECIMAL)]),
			},
			[Validators.required]
		),
		total: new FormControl<number | null>({ value: null, disabled: true }),
		natureAndQuantityOfGoods: ["Consolidation as per att'd list", [Validators.required]],
		destinationCurrencyRate: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<number | null>(null, [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		}),
		destinationCollectCharges: new FormControl<number | null>({ value: null, disabled: true }, [
			Validators.pattern(REGX_NUMBER_1_DECIMAL),
		]),
		totalCollectCharges: new FormControl<number | null>({ value: null, disabled: true }, [Validators.pattern(REGX_NUMBER_2_DECIMAL)]),
		destinationCharges: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<number | null>(null, [Validators.pattern(REGX_NUMBER_2_DECIMAL)]),
		}),
		shippingInfo: [''],
		shippingRefNo: [''],
		//last row
		date: new FormControl<Date | null>(null, [Validators.required]),
		atPlace: ['', [Validators.required]],
		signatureOfShipperOrHisAgent: ['', [Validators.required]],
		signatureOfCarrierOrItsAgent: ['', [Validators.required]],
	});

	displayedColumns: string[] = [
		'noOfPiecesRcp',
		'grossWeight',
		'rateClass',
		'chargeableWeight',
		'rateCharge',
		'total',
		'natureAndQuantityOfGoods',
	];
	dataSource = [{ position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' }];

	selectedTabIndex = 0;
	subscripitonParam: SubscriptionRequest = { groupId: this.mawbId, fromTab: true };

	public shipperInfo: ShipmentParty | null = null;
	public consigneeInfo: ShipmentParty | null = null;
	public currencies: string[] = [];

	readonly mawbModule = Modules.MAWB;
	readonly savePermission = [UserPermission.CREATE, UserPermission.UPDATE].join(',');

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly mawbCreateRequestService: MawbCreateRequestService,
		private readonly notificationService: NotificationService,
		private readonly translateService: TranslateService,
		private readonly dialog: MatDialog,
		private readonly router: Router,
		private readonly datePipe: DatePipe,
		private readonly cdr: ChangeDetectorRef
	) {
		super();

		// get navigation state
		const navigation = this.router.getCurrentNavigation();
		if (navigation?.extras.state) {
			const state = navigation.extras.state;
			this.currentTabLabel = navigation?.extras?.state?.['selectedLabel'] ?? '';
			this.hawbList = state['selectedHawbs'] ?? [];
			this.hawbIdList = this.hawbList.map((item) => item.hawbId);
		}
	}

	onMawbPrefixChange(event: MatSelectChange) {
		const selectedOrgId = event.value;
		if (selectedOrgId) {
			this.orgMgmtRequestService
				.getOrgInfo(selectedOrgId)
				.pipe(takeUntilDestroyed(this.destroyRef))
				.subscribe((orgInfo) => {
					this.issuedByInfo = orgInfo;
					this.cdr.markForCheck();
				});
		}
	}

	mawbNumberValidator(control: AbstractControl): ValidationErrors | null {
		const value = control.value;

		if (!value) {
			return null;
		}

		const numberPattern = /^\d{8}$/;
		if (!numberPattern.test(value)) {
			return { mawbNumberFormat: { message: this.translateService.instant('mawb.formItem.mawbNumber.checkLength') } };
		}

		const first7Digits = value.substring(0, 7);
		const checkDigit = parseInt(value.charAt(7), 10);

		const calculatedCheckDigit = parseInt(first7Digits, 10) % 7;

		if (checkDigit !== calculatedCheckDigit) {
			return {
				mawbNumberCheckDigit: {
					message: this.translateService.instant('mawb.formItem.mawbNumber.checkDigit') + calculatedCheckDigit,
				},
			};
		}

		return null;
	}

	allPropertySame<T>(arr: T[], prop: keyof T): boolean {
		if (arr.length === 0) return true;
		const firstVal = arr[0][prop];
		return arr.every((item) => item[prop] === firstVal);
	}

	goBack(msg: string): void {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translateService.instant(msg),
			},
		});
		dialogRef.afterClosed().subscribe(() => {
			this.router.navigate(['/mawb/create']);
		});
	}

	ngAfterViewInit(): void {
		this.mawbForm
			.get('total')
			?.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				const wtOrVal = this.airPortInfoComponent.airportInfoForm.get('wtOrVal')?.value;
				if (wtOrVal === DropDownType.PREPAID) {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						weightChargePrepaid: value,
					});
				} else {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						weightChargeCollect: value,
					});
				}
			});

		this.airPortInfoComponent.airportInfoForm
			.get('declaredValueForCarriage')
			?.get('numericalValue')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				const wtOrVal = this.airPortInfoComponent.airportInfoForm.get('wtOrVal')?.value;
				const val = REGX_NUMBER_2_DECIMAL.test(value ?? '') ? Number(value) : null;

				if (wtOrVal === DropDownType.PREPAID) {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						valuationChargePrepaid: val,
					});
				} else {
					this.prepaidCollectComponent.prepaidForm.patchValue({
						valuationChargeCollect: val,
					});
				}
			});

		this.airPortInfoComponent.airportInfoForm
			.get('other')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				if (value === DropDownType.PREPAID) {
					this.otherChargesComponent.otherChargesForm.patchValue({
						chargePaymentType: DropDownType.PREPAID,
					});
				} else {
					this.otherChargesComponent.otherChargesForm.patchValue({
						chargePaymentType: DropDownType.COLLECT,
					});
				}
			});

		this.selectTab(this.currentTabLabel);
	}

	ngOnInit(): void {
		const initRequests$ = forkJoin({
			orgList: this.orgMgmtRequestService.getOrgList(OrgType.CARRIER),
			hawbDetailList: !this.mawbId
				? this.mawbCreateRequestService.getHawbDetailBatch(this.hawbList.map((item) => item.hawbId))
				: of([]),
			mawbDetail: this.mawbId ? this.mawbCreateRequestService.getMawbDetail(this.mawbId) : of(null),
		});

		initRequests$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(({ orgList, hawbDetailList, mawbDetail }) => {
			this.carriers = orgList;

			if (this.mawbId) {
				if (mawbDetail) {
					this.fillMawbInfo(mawbDetail);
				}
				this.subscripitonParam = { ...this.subscripitonParam, groupId: this.mawbId };
			} else {
				if (hawbDetailList.length === 0) return;

				this.getCurrentUser()
					.pipe(takeUntilDestroyed(this.destroyRef))
					.subscribe((user) => {
						const orgId = user?.primaryOrgId;
						if (orgId) {
							this.orgMgmtRequestService
								.getOrgInfo(orgId)
								.pipe(takeUntilDestroyed(this.destroyRef))
								.subscribe((result) => {
									this.carrierInfo = result;
									this.cdr.markForCheck();
								});
						}
					});

				const handingInformations = hawbDetailList.map((item) => item.textualHandlingInstructions);
				const isSameWeightValuationIndicator = this.allPropertySame(hawbDetailList, 'weightValuationIndicator');
				const isSameOtherChargesIndicator = this.allPropertySame(hawbDetailList, 'otherChargesIndicator');

				if (!isSameWeightValuationIndicator) {
					this.goBack('mawb.dialog.weight.validate');
				} else {
					this.airPortInfoComponent.airportInfoForm.patchValue({
						wtOrVal: hawbDetailList[0].weightValuationIndicator,
					});
				}

				if (!isSameOtherChargesIndicator) {
					this.goBack('mawb.dialog.other.validate');
				} else {
					this.airPortInfoComponent.airportInfoForm.patchValue({
						other: hawbDetailList[0].otherChargesIndicator,
					});
				}

				let totalGrossWeights = 0;
				let totalPieces = 0;
				hawbDetailList.forEach((item) => {
					totalPieces += item.pieceIdList?.length ?? 0;
					totalGrossWeights += item.totalGrossWeight ?? 0;
					this.otherChargesList.push(...item.otherChargeList);
				});
				this.otherChargesList = this.otherChargesList.map((item) => ({
					...item,
					disabled: true,
				}));

				this.mawbForm.patchValue({
					handingInformation: handingInformations.join(','),
					grossWeight: Number(totalGrossWeights.toFixed(2)),
					noOfPiecesRcp: totalPieces,
				});
			}

			this.cdr.markForCheck();
		});

		this.sliCreateRequestService
			.getCurrencies()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((currencies: string[]) => {
				this.currencies = ['', ...currencies];
			});

		combineLatest([
			this.mawbForm.get('chargeableWeight')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
			this.mawbForm.get('rateCharge')!.get('numericalValue')!.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef)),
		])
			.pipe(
				tap(([chargeableWeight, rateChargeNumber]) => {
					if (Number(chargeableWeight) && Number(rateChargeNumber)) {
						this.mawbForm.patchValue({
							total: Number((Number(chargeableWeight) * Number(rateChargeNumber)).toFixed(2)),
						});
					} else {
						this.mawbForm.patchValue({
							total: 0,
						});
					}
				}),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe();

		this.mawbForm
			.get('destinationCurrencyRate')!
			.get('numericalValue')!
			.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				if (Number(value)) {
					this.mawbForm.patchValue({
						destinationCollectCharges: Number(
							(Number(value) * Number(this.prepaidCollectComponent.prepaidForm.get('totalCollect')?.value)).toFixed(1)
						),
					});
				} else {
					this.mawbForm.patchValue({
						destinationCollectCharges: null,
					});
				}
			});

		this.mawbForm
			.get('destinationCharges')!
			.get('numericalValue')!
			.valueChanges.pipe(startWith(null), takeUntilDestroyed(this.destroyRef))
			.subscribe((value) => {
				if (Number(value)) {
					this.mawbForm.patchValue({
						totalCollectCharges: Number(
							(Number(value) + Number(this.mawbForm.get('destinationCollectCharges')?.value)).toFixed(2)
						),
					});
				} else {
					this.mawbForm.patchValue({
						totalCollectCharges: null,
					});
				}
			});
	}

	fillMawbInfo(mawbDetail: MawbCreateDto): void {
		const {
			orgId,
			waybillPrefix,
			waybillNumber,
			pieceIdList,
			accountingNoteList,
			partyList,
			departureLocation,
			arrivalLocation,
			requestedFlight,
			requestedDate,
			toFirst,
			toSecond,
			toThird,
			byFirstCarrier,
			bySecondCarrier,
			byThirdCarrier,
			insuredAmount,
			carrierChargeCode,
			weightValuationIndicator,
			otherChargesIndicator,
			declaredValueForCarriage,
			declaredValueForCustoms,
			textualHandlingInstructions,
			totalGrossWeight,
			serviceCode,
			rateClassCode,
			totalVolumetricWeight,
			rateCharge,
			goodsDescription,
			otherChargeList,
			destinationCurrencyRate,
			destinationCharges,
			shippingInfo,
			shippingRefNo,
			carrierDeclarationDate,
			carrierDeclarationPlace,
			consignorDeclarationSignature,
			carrierDeclarationSignature,
		} = mawbDetail;

		this.orgId = orgId;
		this.mawbNumber = waybillNumber;
		this.shipperInfo = partyList?.find((item) => item.companyType === OrgType.SHIPPER) ?? null;
		this.consigneeInfo = partyList?.find((item) => item.companyType === OrgType.CONSIGNEE) ?? null;
		this.carrierInfo = partyList?.find((item) => item.companyType === OrgType.FORWARDER) ?? null;
		this.alsoNotifies = partyList?.filter((item) => !item.companyType) ?? [];
		const issuedByInfo = partyList?.find((item) => item.companyType === OrgType.CARRIER) ?? null;
		this.issuedByInfo = {
			id: issuedByInfo?.id ?? '',
			companyName: issuedByInfo?.companyName ?? '',
			countryCode: issuedByInfo?.countryCode ?? '',
			regionCode: issuedByInfo?.regionCode ?? '',
			cityCode: issuedByInfo?.cityCode ?? '',
			textualPostCode: issuedByInfo?.textualPostCode ?? '',
			locationName: issuedByInfo?.locationName ?? '',
			airlineCode: issuedByInfo?.airlineCode ?? '',
			partyRole: '',
			persons: [],
		};

		this.shipperOrConsigneeInfoComponentList.first.shipperConsigneeForm.patchValue({
			accountingNoteText:
				accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.SHIPPER)
					?.accountingNoteText ?? '',
		});
		this.shipperOrConsigneeInfoComponentList.last.shipperConsigneeForm.patchValue({
			accountingNoteText:
				accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.CONSIGNEE)
					?.accountingNoteText ?? '',
		});
		this.carrierAgentComponent.carrierAgentForm.patchValue({
			accountingNoteText:
				accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.FORWARDER)
					?.accountingNoteText ?? '',
		});

		this.airPortInfoComponent.airportInfoForm.patchValue({
			departureAndRequestedRouting: departureLocation,
			airportOfDestination: arrivalLocation,
			amountOfInsurance: {
				id: insuredAmount?.id ?? null,
				currencyUnit: insuredAmount?.currencyUnit ?? '',
				numericalValue: insuredAmount?.numericalValue?.toString() ?? 'NIL',
			},
			flight: requestedFlight,
			to: toFirst,
			toBy2ndCarrier: toSecond,
			toBy3rdCarrier: toThird,
			date: new Date(requestedDate!),
			byFirstCarrier: byFirstCarrier,
			by2ndCarrier: bySecondCarrier,
			by3rdCarrier: byThirdCarrier,
			chargesCode: carrierChargeCode,
			wtOrVal: weightValuationIndicator,
			other: otherChargesIndicator,
			declaredValueForCarriage: {
				currencyUnit: declaredValueForCarriage?.currencyUnit ?? '',
				numericalValue: !declaredValueForCarriage?.numericalValue ? 'NCV' : declaredValueForCarriage?.numericalValue.toString(),
			},
			declaredValueForCustoms: {
				currencyUnit: declaredValueForCustoms?.currencyUnit ?? '',
				numericalValue: !declaredValueForCustoms?.numericalValue ? 'NVD' : declaredValueForCustoms?.numericalValue.toString(),
			},
		});

		this.otherChargesComponent.initOtherCharges(otherChargeList as any);

		this.mawbForm.patchValue({
			mawbPrefix: this.carriers?.find((item) => item.prefix === waybillPrefix)?.id ?? '',
			mawbNumber: waybillNumber,
			accountingInformation:
				accountingNoteList?.find((item) => item.accountingNoteIdentifier === AccountingNoteIdentifier.CARRIER)
					?.accountingNoteText ?? '',
			handingInformation: textualHandlingInstructions,
			noOfPiecesRcp: pieceIdList?.length ?? null,
			grossWeight: totalGrossWeight,
			serviceCode,
			rateClass: rateClassCode,
			chargeableWeight: totalVolumetricWeight,
			rateCharge: {
				currencyUnit: rateCharge?.currencyUnit ?? '',
				numericalValue: rateCharge?.numericalValue ?? null,
			},
			natureAndQuantityOfGoods: goodsDescription,
			destinationCurrencyRate: {
				currencyUnit: destinationCharges?.currencyUnit ?? '',
				numericalValue: destinationCurrencyRate,
			},
			destinationCharges: {
				currencyUnit: destinationCharges?.currencyUnit ?? '',
				numericalValue: destinationCharges?.numericalValue ?? null,
			},
			shippingInfo,
			shippingRefNo,
			date: new Date(carrierDeclarationDate),
			atPlace: carrierDeclarationPlace,
			signatureOfShipperOrHisAgent: consignorDeclarationSignature,
			signatureOfCarrierOrItsAgent: carrierDeclarationSignature,
		});
	}

	onWtOrValChange(event: MatSelectChange) {
		const wtOrVal = event.value;
		const declaredValueForCarriage = this.airPortInfoComponent.airportInfoForm
			.get('declaredValueForCarriage')
			?.get('numericalValue')?.value;
		if (wtOrVal === DropDownType.PREPAID) {
			this.prepaidCollectComponent.prepaidForm.patchValue({
				weightChargePrepaid: this.mawbForm.get('total')?.value,
				weightChargeCollect: null,
				valuationChargePrepaid: REGX_NUMBER_2_DECIMAL.test(declaredValueForCarriage ?? '')
					? Number(declaredValueForCarriage)
					: null,
				valuationChargeCollect: null,
			});
		} else {
			this.prepaidCollectComponent.prepaidForm.patchValue({
				weightChargePrepaid: null,
				weightChargeCollect: this.mawbForm.get('total')?.value,
				valuationChargePrepaid: null,
				valuationChargeCollect: REGX_NUMBER_2_DECIMAL.test(declaredValueForCarriage ?? '')
					? Number(declaredValueForCarriage)
					: null,
			});
		}
	}

	onOtherChargesChange(emitCharges: EmitCharges[]) {
		const isPrepaid = emitCharges.some((item) => item.chargePaymentType === DropDownType.PREPAID);
		const isCollect = emitCharges.some((item) => item.chargePaymentType === DropDownType.COLLECT);

		if (!isPrepaid) {
			this.prepaidCollectComponent?.prepaidForm.patchValue({
				taxPrepaid: null,
				totalOtherChargesDueAgentPrepaid: null,
				totalOtherChargesDueCarrierPrepaid: null,
			});
		}
		if (!isCollect) {
			this.prepaidCollectComponent?.prepaidForm.patchValue({
				taxCollect: null,
				totalOtherChargesDueAgentCollect: null,
				totalOtherChargesDueCarrierCollect: null,
			});
		}

		emitCharges.forEach((item) => {
			const agentCharges = Number(item.agentCharges?.toFixed(2));
			const carrierCharges = Number(item.carrierCharges?.toFixed(2));
			if (item.chargePaymentType === DropDownType.PREPAID) {
				this.prepaidCollectComponent.prepaidForm.patchValue({
					taxPrepaid: item.taxCharges,
					totalOtherChargesDueAgentPrepaid: agentCharges,
					totalOtherChargesDueCarrierPrepaid: carrierCharges,
				});
			} else {
				this.prepaidCollectComponent.prepaidForm.patchValue({
					taxCollect: item.taxCharges,
					totalOtherChargesDueAgentCollect: agentCharges,
					totalOtherChargesDueCarrierCollect: carrierCharges,
				});
			}
		});
	}

	delAlsoNotify(index: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.alsoNotifies.splice(index, 1);
	}

	addAlsoNotify(): void {
		const newNotify = {
			companyName: '',
			contactName: '',
			countryCode: '',
			regionCode: '',
			cityCode: '',
			textualPostCode: '',
			locationName: '',
			phoneNumber: '',
			emailAddress: '',
			companyType: '',
		};
		this.alsoNotifies.push(newNotify);
	}

	getOrgList(idx: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.alsoNotifies = this.alsoNotifies.map((item, index) => {
				if (idx === index) {
					const person = result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT);
					return {
						...item,
						id: result.id,
						companyName: result.companyName,
						contactName: person?.contactName ?? '',
						countryCode: result.countryCode,
						regionCode: result.regionCode,
						cityCode: result.cityCode,
						textualPostCode: result.textualPostCode,
						locationName: result.locationName,
						phoneNumber: person?.phoneNumber ?? '',
						emailAddress: person?.emailAddress ?? '',
						companyType: '',
					};
				}
				return item;
			});

			this.cdr.markForCheck();
		});
	}

	onCancel() {
		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translateService.instant('common.dialog.cancel.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.isConfirmed = true;
				this.router.navigate(['/mawb/create']);
			}
		});
	}

	private markFormGroupTouched(formGroup: FormGroup) {
		Object.values(formGroup.controls).forEach((control) => {
			control.markAsTouched();
			if (control instanceof FormGroup) {
				this.markFormGroupTouched(control);
			}
		});
	}

	onSave() {
		this.mawbForm.markAllAsTouched();
		this.markFormGroupTouched(this.mawbForm.controls.rateCharge);
		this.airPortInfoComponent.airportInfoForm.markAllAsTouched();
		this.carrierAgentComponent.carrierAgentForm.markAllAsTouched();
		this.shipperOrConsigneeInfoComponentList.forEach((comp) => comp.shipperConsigneeForm?.markAllAsTouched());

		const shipperInfoComponent = this.shipperOrConsigneeInfoComponentList.first;
		const consigneeInfoComponent = this.shipperOrConsigneeInfoComponentList.last;

		const shipperData = shipperInfoComponent?.getFormData();
		const consigneeData = consigneeInfoComponent?.getFormData();
		const carrierAgentData = this.carrierAgentComponent?.getFormData();
		const issuedByData = this.issuedByComponent.getData();

		if (
			!shipperData ||
			!consigneeData ||
			!carrierAgentData ||
			!issuedByData ||
			this.mawbForm.invalid ||
			this.airPortInfoComponent.airportInfoForm.invalid
		) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translateService.instant('common.dialog.form.validate'),
				},
			});

			return;
		}

		const accountingNoteList = [
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.SHIPPER,
				accountingNoteText: shipperInfoComponent?.shipperConsigneeForm.value.accountingNoteText ?? '',
			},
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.CONSIGNEE,
				accountingNoteText: consigneeInfoComponent?.shipperConsigneeForm.value.accountingNoteText ?? '',
			},
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.FORWARDER,
				accountingNoteText: this.carrierAgentComponent?.carrierAgentForm.value.accountingNoteText ?? '',
			},
			{
				accountingNoteIdentifier: AccountingNoteIdentifier.CARRIER,
				accountingNoteText: this.mawbForm?.value.accountingInformation ?? '',
			},
		];

		const {
			mawbPrefix,
			mawbNumber,
			handingInformation,
			grossWeight,
			serviceCode,
			rateClass,
			chargeableWeight,
			rateCharge,
			natureAndQuantityOfGoods,
			date,
			destinationCharges,
			destinationCurrencyRate,
			shippingInfo,
			shippingRefNo,
			atPlace,
			signatureOfShipperOrHisAgent,
			signatureOfCarrierOrItsAgent,
		} = this.mawbForm.value;

		const airPortInfo = this.airPortInfoComponent.airportInfoForm.getRawValue();

		const otherCharges: OtherChargeList[] = this.otherChargesComponent.otherChargesList;
		const alsoNotifies: PartyList[] = this.alsoNotifies;

		const saveData = {
			waybillPrefix: this.carriers.find((item) => item.id === mawbPrefix)?.prefix ?? '',
			waybillNumber: mawbNumber!,
			houseWaybills: this.hawbList.map((item) => item.hawbId),
			partyList: [shipperData, consigneeData, carrierAgentData, issuedByData, ...alsoNotifies],
			accountingNoteList,
			departureLocation: airPortInfo.departureAndRequestedRouting ?? '',
			arrivalLocation: airPortInfo.airportOfDestination ?? '',
			requestedFlight: airPortInfo.flight ?? '',
			requestedDate: this.datePipe.transform(airPortInfo.date, DATE_FORMAT) ?? '',
			toFirst: airPortInfo.to ?? '',
			toSecond: airPortInfo.toBy2ndCarrier ?? '',
			toThird: airPortInfo.toBy3rdCarrier ?? '',
			byFirstCarrier: airPortInfo.byFirstCarrier ?? '',
			bySecondCarrier: airPortInfo.by2ndCarrier ?? '',
			byThirdCarrier: airPortInfo.by3rdCarrier ?? '',
			insuredAmount: {
				id: airPortInfo.amountOfInsurance?.id ?? null,
				currencyUnit: rateCharge?.currencyUnit ?? '', // use the currency unit of rateCharge
				numericalValue: REGX_NUMBER_2_DECIMAL.test(airPortInfo.amountOfInsurance.numericalValue ?? '')
					? Number(airPortInfo.amountOfInsurance.numericalValue)
					: null,
			},
			carrierChargeCode: airPortInfo.chargesCode ?? '',
			weightValuationIndicator: airPortInfo.wtOrVal ?? '',
			otherChargesIndicator: airPortInfo.other ?? '',
			declaredValueForCarriage: {
				currencyUnit: rateCharge?.currencyUnit ?? '', // use the currency unit of rateCharge
				numericalValue: this.getNumericalValue(airPortInfo.declaredValueForCarriage),
			},
			declaredValueForCustoms: {
				currencyUnit: rateCharge?.currencyUnit ?? '', // use the currency unit of rateCharge
				numericalValue: this.getNumericalValue(airPortInfo.declaredValueForCustoms),
			},
			textualHandlingInstructions: handingInformation ?? '',
			totalGrossWeight: !isBlank(grossWeight) ? Number(grossWeight) : null,
			serviceCode: serviceCode ?? null,
			rateClassCode: rateClass ?? null,
			totalVolumetricWeight: !isBlank(chargeableWeight) ? Number(chargeableWeight) : null,
			rateCharge: {
				currencyUnit: rateCharge?.currencyUnit ?? '',
				numericalValue: rateCharge?.numericalValue ? Number(rateCharge?.numericalValue) : null,
			},
			goodsDescription: natureAndQuantityOfGoods ?? '',
			otherChargeList: otherCharges,
			destinationCurrencyRate: destinationCurrencyRate?.numericalValue ? Number(destinationCurrencyRate?.numericalValue) : null,
			destinationCharges: {
				currencyUnit: destinationCurrencyRate?.currencyUnit ?? '', // use the currency unit of destinationCurrencyRate
				numericalValue: destinationCharges?.numericalValue ? Number(destinationCharges?.numericalValue) : null,
			},
			shippingInfo: shippingInfo ?? '',
			shippingRefNo: shippingRefNo ?? '',
			carrierDeclarationDate: this.datePipe.transform(date, DATE_FORMAT) ?? '',
			carrierDeclarationPlace: atPlace!,
			consignorDeclarationSignature: signatureOfShipperOrHisAgent!,
			carrierDeclarationSignature: signatureOfCarrierOrItsAgent!,
		};

		if (!this.mawbId) {
			this.mawbCreateRequestService
				.createMawb(saveData)
				.pipe(
					tap(() => {
						this.notificationService.showSuccess(this.translateService.instant('mawb.createMawb.success'));
						this.router.navigate(['/mawb']);
					})
				)
				.subscribe();
		}

		if (this.mawbId) {
			this.mawbCreateRequestService
				.updateMawb(this.mawbId, saveData)
				.pipe(
					tap(() => {
						this.notificationService.showSuccess(this.translateService.instant('mawb.updateMawb.success'));
						this.router.navigate(['/mawb']);
					})
				)
				.subscribe();
		}
	}

	getNumericalValue(val: any) {
		return val?.numericalValue ? this.getNumValue(val.numericalValue) : 0;
	}

	getNumValue(value: any): number {
		const num = Number(value);
		return isNaN(num) ? 0 : num;
	}

	onTabChanged($event: MatTabChangeEvent) {
		this.currentTabLabel = $event.tab.textLabel;
		this.selectedTabIndex = $event.index;
		if (this.currentTabLabel === this.translateService.instant('subscription.title')) {
			this.subscripitonParam = { ...this.subscripitonParam };
		}
		if (this.currentTabLabel === this.translateService.instant('common.delegation.request.tab')) {
			this.delegationRequestList.refreshData();
		}
	}

	selectTab(label: string): void {
		const index = Array.from(this.tabGroup._tabs).findIndex((tab) => tab.textLabel === label);
		if (index !== -1) {
			this.selectedTabIndex = index;
		} else {
			this.selectedTabIndex = 0;
		}
	}
}
