<div class="orll-subscription-configuration-list iata-box">
	<div class="orll-subscription-configuration-list__search_bar row">
		<div class="col-7">
			<form [formGroup]="configrationSearchForm">
				<div class="orll-subscription-configuration-list__search-form width-100">
					<mat-form-field appearance="outline" class="orll-subscription-configuration-list__input width-100" floatLabel="always">
						<mat-icon matPrefix>search</mat-icon>
						<mat-label>{{ 'common.dialog.delegation.request.description' | translate }}</mat-label>
						<input matInput formControlName="description" />
					</mat-form-field>
					<button mat-stroked-button color="primary" (click)="onSearch()" fxLayout="row" fxLayoutAlign="center center">
						<mat-icon>search</mat-icon>
						{{ 'sli.mgmt.search' | translate }}
					</button>
				</div>
			</form>
		</div>
		<div class="col-5 orll-subscription-configuration-list__btn">
			<button mat-stroked-button color="primary" (click)="inviteToSubscribe()" fxLayout="row" fxLayoutAlign="center center">
				<mat-icon>search</mat-icon>
				{{ 'subscription.btn.invite' | translate }}
			</button>
			<button
				mat-flat-button
				color="primary"
				(click)="createOrUpdateConfiguration(undefined)"
				fxLayout="row"
				fxLayoutAlign="center center">
				<mat-icon>add</mat-icon>
				{{ 'subscription.btn.new' | translate }}
			</button>
		</div>
	</div>
	<orll-table [param]="configurationParam" [service]="configurationService" [columns]="columns"></orll-table>
</div>
