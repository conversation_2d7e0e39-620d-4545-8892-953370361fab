import { Component, ChangeDetectorRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { PartnerAccessRequestService } from '../../services/partner-access-request.service';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { BizData, PartnerListObject } from '../../models/partner-access.model';
import { Organization } from '@shared/models/organization.model';
import { map } from 'rxjs';

@Component({
	selector: 'orll-partner-show',
	imports: [
		CommonModule,
		FormsModule,
		TranslateModule,
		MatTableModule,
		MatButtonModule,
		MatIconModule,
		MatSelectModule,
		MatFormFieldModule,
		MatCheckboxModule,
		ReactiveFormsModule,
	],
	templateUrl: './partner-access.component.html',
	styleUrls: ['./partner-access.component.scss'],
})
export default class PartnerAccessComponent implements OnInit {
	readonly displayedColumns: string[] = [
		'businessData',
		'partner',
		'getLogisticsObject',
		'patchLogisticsObject',
		'postLogisticsEvent',
		'getLogisticsEvent',
	];
	readonly listObjeNames: BizData[] = [
		{
			name: 'GET_LOGISTICS_OBJECT',
			code: 'getLogisticsObject',
		},
		{
			name: 'PATCH_LOGISTICS_OBJECT',
			code: 'patchLogisticsObject',
		},
		{
			name: 'POST_LOGISTICS_EVENT',
			code: 'postLogisticsEvent',
		},
		{
			name: 'GET_LOGISTICS_EVENT',
			code: 'getLogisticsEvent',
		},
	];

	dataLoading = false;

	lastAddedRowIndex: number | null = null;
	dataSource = new MatTableDataSource<PartnerListObject>([]);

	orgList: Organization[] = [];
	bizTypeList: BizData[] = [];
	deletedIds: Set<string> = new Set<string>();

	isEditMode = false;
	newRow: PartnerListObject | null = null;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	constructor(
		private readonly partnerAccessRequestService: PartnerAccessRequestService,
		private readonly cdr: ChangeDetectorRef
	) {}

	addOrSaveRow() {
		if (this.newRow === null) {
			this.newRow = {
				id: '',
				businessData: '',
				orgId: '',
				orgName: '',
				getLogisticsObject: '1',
				patchLogisticsObject: '1',
				postLogisticsEvent: '1',
				getLogisticsEvent: '1',
				createTime: '',
			};
			this.dataSource.data = [this.newRow, ...this.dataSource.data];
			this.cdr.detectChanges();
		} else {
			this.dataLoading = true;
			this.partnerAccessRequestService.addPartner(this.newRow).subscribe({
				next: (res) => {
					this.newRow = null;
					this.dataLoading = false;
					if (this.dataSource.data.length > 0) {
						this.dataSource.data[0].id = res;
						this.dataSource.data = [...this.dataSource.data];
					}
				},
				error: () => {
					this.dataLoading = false;
					const data = [...this.dataSource.data];
					data.shift();
					this.dataSource.data = data;
					this.newRow = null;
				},
			});
		}
	}

	toggleEdit() {
		this.isEditMode = !this.isEditMode;
		if (!this.isEditMode) {
			this.dataLoading = true;
			this.partnerAccessRequestService.updatePartners(this.dataSource.data).subscribe({
				next: () => {
					this.isEditMode = false;
					this.dataLoading = false;
				},
				error: () => {
					this.dataLoading = false;
				},
			});
			if (this.deletedIds.size > 0) {
				this.dataLoading = true;
				this.partnerAccessRequestService.deletePartners([...this.deletedIds]).subscribe({
					next: () => {
						this.isEditMode = false;
						this.deletedIds.clear();
						this.dataLoading = false;
					},
					error: () => {
						this.dataLoading = false;
					},
				});
			}
		}
	}

	shouldShowEditUI(row: PartnerListObject): boolean {
		// these fields are empty, then delete the record
		if (
			row.getLogisticsEvent === '0' &&
			row.getLogisticsObject === '0' &&
			row.patchLogisticsObject === '0' &&
			row.postLogisticsEvent === '0'
		) {
			if (this.newRow !== null) {
				const data = [...this.dataSource.data];
				data.shift();
				this.newRow = null;
				this.cdr.detectChanges();
				this.dataSource.data = data;
			} else {
				this.dataSource.data = this.dataSource.data.filter((item) => item.id !== row.id);
				this.deletedIds.add(row.id);
			}
		}
		return (
			this.isEditMode ||
			(this.newRow !== null && !row.id) ||
			!row.orgId ||
			!row.businessData ||
			(!row.getLogisticsEvent && !row.getLogisticsObject && !row.patchLogisticsObject && !row.postLogisticsEvent)
		);
	}

	getPartnerListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.partnerAccessRequestService
			.getPartnerList(pageParams, '')
			.pipe(
				map((data) =>
					data.rows.map((item) => ({ ...item, orgName: this.orgList.find((org) => item.orgId === org.id)?.name || '' }))
				)
			)
			.subscribe({
				next: (res) => {
					this.dataSource.data = res;
					this.cdr.markForCheck();
					this.dataLoading = false;
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}

	ngOnInit() {
		this.partnerAccessRequestService.getSystemList('businessType').subscribe({
			next: (res) => {
				this.bizTypeList = res;
				this.cdr.markForCheck();
			},
		});

		this.partnerAccessRequestService.getOrgList('', '').subscribe({
			next: (res) => {
				this.orgList = res;
				this.getPartnerListPage(this.pageParams);
				this.cdr.markForCheck();
			},
		});
	}

	onCheckboxChange(event: MatCheckboxChange, item: any, field: string) {
		item[field] = event.checked ? '1' : '0';
	}

	setOrgInfo(event: MatSelectChange, row: PartnerListObject) {
		row.orgId = event.value;
		row.orgName = this.orgList.find((org) => org.id === row.orgId)?.name || '';
	}
}
