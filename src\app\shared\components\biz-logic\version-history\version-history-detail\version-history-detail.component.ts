import { Component, Inject, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogContent, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import {
	OperationObj,
	RequestStatusChangeAction,
	VersionDialogData,
	VersionHistoryDetailObj,
} from '@shared/models/biz-logic/version-history.model';
import { ShareType } from '@shared/models/share-type.model';
import { VersionHistoryService } from '@shared/services/biz-logic/verion-history/version-history.service';

@Component({
	selector: 'orll-version-history-detail',
	imports: [MatDialogContent, MatDialogModule, TranslateModule, MatIconModule, MatButtonModule, MatTableModule],
	templateUrl: './version-history-detail.component.html',
	styleUrl: './version-history-detail.component.scss',
})
export class VersionHistoryDetailComponent implements OnInit {
	@Input() type!: ShareType;

	dataLoading = false;

	dataSource = new MatTableDataSource<OperationObj>([]);
	detail: VersionHistoryDetailObj | null = null;
	displayedColumns = ['loType', 'property', 'oldValue', 'newValue'];

	statusChangeAction = RequestStatusChangeAction;

	constructor(
		private readonly dialogRef: MatDialogRef<VersionHistoryDetailComponent>,
		private readonly service: VersionHistoryService,
		@Inject(MAT_DIALOG_DATA) public data: VersionDialogData
	) {}

	ngOnInit(): void {
		this.service.getVersionHistoryDetail(this.data.actionRequestUri).subscribe((res) => {
			this.detail = res;
			this.dataSource.data = res.hasOperation;
		});
	}

	updateRequestStatus(action: RequestStatusChangeAction) {
		if (this.data) {
			this.dataLoading = true;
			this.service.updateRequestStatus(this.data.actionRequestUri, action).subscribe(() => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			});
		}
	}

	// eslint-disable-next-line @typescript-eslint/naming-convention
	trackById(_index: number, row: OperationObj) {
		return row.id;
	}
}
