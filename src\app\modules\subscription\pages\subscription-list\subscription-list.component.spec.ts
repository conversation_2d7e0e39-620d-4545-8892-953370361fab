import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import SubscriptionListComponent from './subscription-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { SubscriptionRequestService } from '../../services/subscription-request.service';
import { SubscriptionConfigurationListObj, SubscriptionListObj } from '../../models/subscription.model';
import { of } from 'rxjs';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { UserProfileService } from '@shared/services/user-profile.service';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';

const createTabChangeEvent = (label: string, index: number): MatTabChangeEvent => {
	return {
		index,
		tab: {
			textLabel: label,
			content: null!,
			position: null,
			origin: null,
			disabled: false,
			ariaLabel: null,
			ariaControls: null,
			id: null,
		} as any,
	};
};

describe('SubscriptionListComponent', () => {
	let component: SubscriptionListComponent;
	let fixture: ComponentFixture<SubscriptionListComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionRequestService>;
	let mockOrgMgmtRequestService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockSubscriptionConfigService: jasmine.SpyObj<SubscriptionConfigurationService>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		mockSubscriptionService = jasmine.createSpyObj<SubscriptionRequestService>('SubscriptionService', [
			'loadAllData',
			'getDataPerPage',
		]);
		const mockRes: SubscriptionListObj[] = [
			{
				topic: '111',
				topicType: '111',
				createDate: '',
				approvedDate: '',
				subscriptionType: '',
				status: '',
				isRequestedBy: '',
				isRequestedAt: '',
				subscriberOrgName: '',
				orgId: '',
				id: '',
			},
		];
		mockSubscriptionService.getDataPerPage.and.returnValue(of({ total: 55, rows: mockRes }));
		mockOrgMgmtRequestService = jasmine.createSpyObj<OrgMgmtRequestService>('OrgMgmtRequestService', ['getOrgList']);
		mockOrgMgmtRequestService.getOrgList.and.returnValue(of([{ id: '111', name: '111', orgType: '1111' }]));

		mockSubscriptionConfigService = jasmine.createSpyObj<SubscriptionConfigurationService>('SubscriptionConfigurationService', [
			'getDataPerPage',
			'loadAllData',
		]);

		const mockConfigRes: SubscriptionConfigurationListObj[] = [
			{
				id: '333',
				topic: '333',
				topicType: '333',
				subscriptionType: '',
				subscriptionEventType: '',
				description: '',
				expiresAt: '',
				userId: '',
				orgId: '',
				subscriberId: '',
				createAt: '',
				subscriptionRequestUri: '',
			},
		];
		mockSubscriptionConfigService.getDataPerPage.and.returnValue(of({ total: 55, rows: mockConfigRes }));

		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [SubscriptionListComponent, TranslateModule.forRoot()],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			providers: [
				{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
				{ provide: SubscriptionRequestService, useValue: mockSubscriptionService },
				{ provide: SubscriptionConfigurationService, useValue: mockSubscriptionConfigService },
				{ provide: UserProfileService, useValue: userProfileServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SubscriptionListComponent);
		component = fixture.componentInstance;
		component.currentTabLabel = '';
		component.selectedTabIndex = 0;
		component.requestParam = { fromTab: false };

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('onTabChanged', () => {
		it('should update currentTabLabel and selectedTabIndex', () => {
			const event = createTabChangeEvent('Notification', 1);

			component.onTabChanged(event);

			expect(component.currentTabLabel).toBe('Notification');
			expect(component.selectedTabIndex).toBe(1);
		});

		it('should update requestParam when switching to "Request" tab', () => {
			const original = component.requestParam;
			const event = createTabChangeEvent('Request', 0);

			component.onTabChanged(event);
			expect(component.requestParam).toEqual(original);
		});

		it('should NOT update requestParam when switching to other tabs', () => {
			const event = createTabChangeEvent('Notification', 1);
			const original = component.requestParam;

			component.onTabChanged(event);

			expect(component.requestParam).toBe(original);
		});
	});
});
