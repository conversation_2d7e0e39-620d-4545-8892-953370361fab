.orll-mawb-status {
	&__mawb,
	&__panel_header {
		padding: 10px 10px;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
	}
	&__hawb {
		background-color: var(--iata-white);
		border-radius: 8px;
		margin-bottom: 10px;
		border: 1px solid var(--iata-grey-200);
		.orll-mawb-status__checkbox_col {
			padding-left: 40px;
		}
	}
	&__btn ,&__history{
		cursor: pointer;
	}
	&__col_status {
		color : var(--iata-blue-600);
		text-decoration: underline;

	}

	&__panel_header {
		margin-bottom: 0px;
	}
	&__checkbox_col {
		width: 300px;
	}

	.virtual-scroll-viewport {
		height: 600px;
	}
	&__piece {
		background-color: var(--iata-grey-50);
		max-height: 300px;
		margin: 20px;
		min-width: 0;
		overflow-y: auto;
		overflow-x: hidden;
		.orll-mawb-status__panel_header {
			margin: 0px, 20px;
			border-top: 1px solid #e4e6ee !important;
		}
	}
}
