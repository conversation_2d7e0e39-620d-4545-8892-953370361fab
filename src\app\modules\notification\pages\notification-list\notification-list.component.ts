import { Component } from '@angular/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationComponent } from '../../components/notification/notification.component';
import { NotificationListObj } from '../../models/notification.model';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
	selector: 'orll-notification-list',
	imports: [TranslateModule, MatSlideToggleModule, NotificationComponent, MatIconModule, MatButtonModule],
	templateUrl: './notification-list.component.html',
	styleUrl: './notification-list.component.scss',
})
export default class NotificationListComponent {
	notifications: NotificationListObj[] = [];

	markAllViewed(event: MouseEvent) {
		event.preventDefault();
		//will be implement later
	}
}
