import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { HawbTableComponent } from './hawb-table.component';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, SimpleChange } from '@angular/core';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';

describe('HawbTableComponent', () => {
	let component: HawbTableComponent;
	let fixture: ComponentFixture<HawbTableComponent>;
	let router: jasmine.SpyObj<Router>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	// Mock data
	const mockRecords: HawbListObject[] = [
		{
			hawbNumber: 'H000920',
			shipper: 'Shipper A',
			consignee: 'Consignee A',
			goodsDescription: 'Goods A',
			origin: 'DEP A',
			destination: 'ARR A',
			mawbNumber: 'M1',
			createDate: '2023-04-13',
			orgId: '123',
			sliId: 'S000001',
			hawbId: 'H000001',
			mawbId: 'M000001',
		},
		{
			hawbNumber: 'S000921',
			shipper: 'Shipper B',
			consignee: 'Consignee B',
			goodsDescription: 'Goods B',
			origin: 'DEP B',
			destination: 'ARR B',
			mawbNumber: 'M2',
			createDate: '2023-04-14',
			orgId: '123',
			sliId: 'S000002',
			hawbId: 'H000002',
			mawbId: 'M000002',
		},
		{
			hawbNumber: 'S000922',
			shipper: 'Shipper C',
			consignee: 'Consignee C',
			goodsDescription: 'Goods C',
			origin: 'DEP C',
			destination: 'ARR C',
			mawbNumber: 'M3',
			createDate: '2023-04-15',
			orgId: '123',
			sliId: 'S000003',
			hawbId: 'H000003',
			mawbId: 'M000003',
		},
	];

	// Mock data with missing fields
	const mockIncompleteRecords: HawbListObject[] = [
		{
			hawbNumber: 'S000923',
			shipper: '',
			consignee: 'Consignee D',
			goodsDescription: 'Goods D',
			origin: 'DEP D',
			destination: 'ARR D',
			mawbNumber: '',
			createDate: '2023-04-16',
			orgId: '123',
			sliId: 'S000004',
			hawbId: 'H000004',
			mawbId: 'M000004',
		},
		{
			hawbNumber: 'S000924',
			shipper: 'Shipper E',
			consignee: '',
			goodsDescription: '',
			origin: 'DEP E',
			destination: 'ARR E',
			mawbNumber: 'M5',
			createDate: '2023-04-17',
			orgId: '123',
			sliId: 'S000005',
			hawbId: 'H000005',
			mawbId: 'M000005',
		},
	];

	beforeEach(async () => {
		const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [HawbTableComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			providers: [
				{
					provide: ActivatedRoute,
					useClass: class ActivatedRouteMock {},
				},
				{
					provide: Router,
					useValue: routerSpy,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(HawbTableComponent);
		component = fixture.componentInstance;

		// Initialize required pagination params
		component.pageParams = {
			pageNum: 1,
			pageSize: 10,
			orderByColumn: '',
			isAsc: 'asc',
		};

		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with empty records array', () => {
			expect(component.records).toEqual([]);
			expect(component.dataSource.data).toEqual([]);
		});

		it('should initialize with correct page size options', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});
	});

	describe('Data Handling', () => {
		it('should update dataSource when records input changes', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should not update dataSource when other inputs change', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });

			// Set initial state
			expect(component.dataSource.data).toEqual(mockRecords);

			// Change totalRecords
			component.totalRecords = 100;
			component.ngOnChanges({ totalRecords: new SimpleChange(0, 100, true) });

			// DataSource should remain unchanged
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should generate correct tracking key with trackByHawbId', () => {
			const record = mockRecords[0];
			const key = component.trackByHawbId(record);
			expect(key).toEqual('H0000012023-04-13');
		});

		it('should handle empty records array', () => {
			component.records = [];
			component.ngOnChanges({ records: new SimpleChange(mockRecords, [], false) });
			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle records with missing data', () => {
			component.records = mockIncompleteRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockIncompleteRecords, true) });
			expect(component.dataSource.data).toEqual(mockIncompleteRecords);

			// Verify the table renders correctly with incomplete data
			fixture.detectChanges();
			const rows = fixture.debugElement.queryAll(By.css('tr.orll-hawb-table__row'));
			expect(rows.length).toBe(mockIncompleteRecords.length);
		});

		it('should handle null records gracefully', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(mockRecords, null, false) });
			// When null is passed, the component should default to an empty array
			expect(component.dataSource.data).toEqual(mockRecords);
		});
	});

	describe('Navigation', () => {
		it('should navigate to create route when createHawbFromSli is called', fakeAsync(() => {
			component.createHawbFromSli();
			tick();

			expect(router.navigate).toHaveBeenCalledWith(['/hawb/create']);
		}));

		it('should navigate to edit route with hawb id when editHawb is called', () => {
			const hawbId = 'H000920';
			component.editHawb(hawbId);
			expect(router.navigate).toHaveBeenCalledWith(['/hawb/edit', 'H000920']);
		});

		it('should navigate to edit route with empty hawb id when editHawb is called with empty string', () => {
			const hawbId = '';
			component.editHawb(hawbId);
			expect(router.navigate).toHaveBeenCalledWith(['/hawb/edit', hawbId]);
		});
	});

	describe('Event Handling', () => {
		it('should emit shareHawb event when share button is clicked', () => {
			// Setup
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			fixture.detectChanges();

			// Spy on the output event
			spyOn(component.shareHawb, 'emit');

			// Find and click the first share button
			const shareButtons = fixture.debugElement.queryAll(By.css('button.share-button'));
			expect(shareButtons.length).toBeGreaterThan(0);
			shareButtons[0].nativeElement.click();

			// Verify the event was emitted with the correct record
			expect(component.shareHawb.emit).toHaveBeenCalledWith(mockRecords[0]);
		});

		it('should emit sortChange event when sort is changed', fakeAsync(() => {
			// Setup
			spyOn(component.sortChange, 'emit');

			// Manually trigger the sort event
			const sortEvent: Sort = { active: 'waybillNumber', direction: 'asc' };
			component.onSortChange(sortEvent);
			tick();

			// Verify the event was emitted with the correct sort parameters
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		}));

		it('should emit pagination event when page is changed', () => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Create a page event
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 50,
				length: 100,
			};

			// Get the paginator and trigger the page event
			const paginator = fixture.debugElement.query(By.directive(MatPaginator));
			if (paginator) {
				const paginatorComponent = paginator.componentInstance as MatPaginator;
				paginatorComponent.page.emit(pageEvent);

				// Verify the event was emitted with the correct page parameters
				expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
			}
		});

		it('should emit pagination with sort information when emitPaginationWithSort is called', fakeAsync(() => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Set current sort
			const sortEvent: Sort = { active: 'shipper', direction: 'desc' };
			component.currentSort = sortEvent;

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort();
			tick();

			// Verify the event was emitted with the correct parameters
			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: component.pageParams.pageNum - 1,
				pageSize: component.pageParams.pageSize,
				length: component.totalRecords,
				sortField: 'shipper',
				sortDirection: 'desc',
			});
		}));

		it('should emit pagination with sort and custom page event when emitPaginationWithSort is called with page event', fakeAsync(() => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Set current sort
			const sortEvent: Sort = { active: 'consignee', direction: 'asc' };
			component.currentSort = sortEvent;

			// Create a page event
			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 25,
				length: 150,
			};

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort(pageEvent);
			tick();

			// Verify the event was emitted with the correct parameters
			expect(component.pagination.emit).toHaveBeenCalledWith({
				...pageEvent,
				sortField: 'consignee',
				sortDirection: 'asc',
			});
		}));
	});

	describe('UI Elements', () => {
		beforeEach(() => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			fixture.detectChanges();
		});

		it('should display the correct number of rows in the table', () => {
			const rows = fixture.debugElement.queryAll(By.css('tr.orll-hawb-table__row'));
			expect(rows.length).toBe(mockRecords.length);
		});

		it('should display the create button with correct text', () => {
			const createButton = fixture.debugElement.query(By.css('button[color="primary"]'));
			expect(createButton).toBeTruthy();
			// Note: We can't check the text directly due to the translation pipe
		});

		it('should call createHawbFromSli when create button is clicked', () => {
			spyOn(component, 'createHawbFromSli');
			const createButton = fixture.debugElement.query(By.css('button[color="primary"]'));
			createButton.nativeElement.click();
			expect(component.createHawbFromSli).toHaveBeenCalled();
		});

		it('should call editHawb when a hawb number link is clicked', () => {
			spyOn(component, 'editHawb');
			const hawbNumberLinks = fixture.debugElement.queryAll(By.css('.hawb-number__link'));
			expect(hawbNumberLinks.length).toBeGreaterThan(0);
			hawbNumberLinks[0].nativeElement.click();
			expect(component.editHawb).toHaveBeenCalledWith(mockRecords[0].hawbId);
		});

		it('should display all table columns correctly', () => {
			const headerCells = fixture.debugElement.queryAll(By.css('th[mat-header-cell]'));
			expect(headerCells.length).toBe(component.displayedColumns.length);
		});

		it('should display correct data in each cell', () => {
			const firstRow = fixture.debugElement.queryAll(By.css('tr.orll-hawb-table__row'))[0];
			const cells = firstRow.queryAll(By.css('td'));

			// Check waybill number (first column)
			expect(cells[0].query(By.css('.hawb-number__link')).nativeElement.textContent.trim()).toBe(mockRecords[0].hawbNumber);

			// Check shipper (second column)
			expect(cells[1].nativeElement.textContent.trim()).toBe(mockRecords[0].shipper);

			// Check consignee (third column)
			expect(cells[2].nativeElement.textContent.trim()).toBe(mockRecords[0].consignee);
		});
	});

	describe('Edge Cases', () => {
		it('should handle empty string values in records', () => {
			component.records = mockIncompleteRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockIncompleteRecords, true) });
			fixture.detectChanges();

			// Get the first row (which has empty shipper)
			const firstRow = fixture.debugElement.queryAll(By.css('tr.orll-hawb-table__row'))[0];
			const cells = firstRow.queryAll(By.css('td'));

			// Check shipper cell (second column) - should be empty
			expect(cells[1].nativeElement.textContent.trim()).toBe('');

			// Get the second row (which has empty consignee and goodsDescription)
			const secondRow = fixture.debugElement.queryAll(By.css('tr.orll-hawb-table__row'))[1];
			const secondRowCells = secondRow.queryAll(By.css('td'));

			// Check consignee cell (third column) - should be empty
			expect(secondRowCells[2].nativeElement.textContent.trim()).toBe('');

			// Check goodsDescription cell (fourth column) - should be empty
			expect(secondRowCells[3].nativeElement.textContent.trim()).toBe('');
		});

		it('should handle sorting with empty values', fakeAsync(() => {
			// Setup with records containing empty values
			component.records = [...mockRecords, ...mockIncompleteRecords];
			component.ngOnChanges({ records: new SimpleChange(null, component.records, true) });
			fixture.detectChanges();

			// Spy on the sort change event
			spyOn(component.sortChange, 'emit');

			// Trigger sort on a column with empty values (shipper)
			const sortEvent: Sort = { active: 'shipper', direction: 'asc' };
			component.onSortChange(sortEvent);
			tick();

			// Verify the event was emitted with the correct sort parameters
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		}));
	});
});
