import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DelegationRequestTableComponent } from './delegation-request-table.component';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { PageEvent, MatPaginatorIntl } from '@angular/material/paginator';
import { SimpleChange } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Subject, of } from 'rxjs';
import { Sort } from '@angular/material/sort';
import { DelegationRequest } from '@shared/models/delegation-request';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { DelegationRequestService } from '@shared/services/delegation/delegation-request.service';
import { DelegationRequestDialogComponent } from '@shared/components/delegation-request-dialog/delegation-request-dialog.component';

describe('DelegationRequestTableComponent', () => {
	let component: DelegationRequestTableComponent;
	let fixture: ComponentFixture<DelegationRequestTableComponent>;
	let mockDelegationRequests: DelegationRequest[];
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockPaginatorIntl: jasmine.SpyObj<MatPaginatorIntl>;
	let mockDelegationRequestService: jasmine.SpyObj<DelegationRequestService>;

	beforeEach(async () => {
		mockDelegationRequests = [
			{
				id: '1',
				isRequestedFor: ['123'],
				isRequestedBy: 'abc',
				isRequestedAt: '2023-01-01',
				requestStatus: 'pending',
				hasDescription: '123456',
				hasPermission: ['234'],
				hasLogisticsObject: ['345'],
			},
			{
				id: '2',
				isRequestedFor: ['123456'],
				isRequestedBy: 'abcd',
				isRequestedAt: '2024-01-01',
				requestStatus: 'approved',
				hasDescription: '1234567',
				hasPermission: ['2345'],
				hasLogisticsObject: ['3456'],
			},
		];

		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockDelegationRequestService = jasmine.createSpyObj('DelegationRequestService', ['getDelegationDetail']);

		// Create mock MatPaginatorIntl
		mockPaginatorIntl = jasmine.createSpyObj('MatPaginatorIntl', [], {
			changes: new Subject(),
			itemsPerPageLabel: 'Items per page:',
			nextPageLabel: 'Next page',
			previousPageLabel: 'Previous page',
			firstPageLabel: 'First page',
			lastPageLabel: 'Last page',
		});

		await TestBed.configureTestingModule({
			imports: [DelegationRequestTableComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: MatPaginatorIntl, useValue: mockPaginatorIntl },
				{ provide: DelegationRequestService, useValue: mockDelegationRequestService },
				provideHttpClient(),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(DelegationRequestTableComponent);
		component = fixture.componentInstance;

		// Mock dependencies
		component.dataSource = new MatTableDataSource<DelegationRequest>(mockDelegationRequests);
		component.pageParams = { pageNum: 1, pageSize: 10 };
		component.totalRecords = 20;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnChanges', () => {
		it('should update dataSource when records change', () => {
			// Set up the component with initial empty data
			component.dataSource = new MatTableDataSource<DelegationRequest>([]);

			// Create a changes object with the records property
			const changes = {
				records: new SimpleChange(null, mockDelegationRequests, true),
			};

			// Set the records input property
			component.records = mockDelegationRequests;

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data contains the mock delegation requests
			expect(component.dataSource.data.length).toEqual(2);
			expect(component.dataSource.data[0]).toEqual(mockDelegationRequests[0]);
			expect(component.dataSource.data[1]).toEqual(mockDelegationRequests[1]);
		});

		it('should handle empty records array', () => {
			const changes = {
				records: new SimpleChange(mockDelegationRequests, [], false),
			};

			component.records = [];
			component.ngOnChanges(changes);

			expect(component.dataSource.data.length).toBe(0);
		});

		it('should not update dataSource when records do not change', () => {
			// Set up the component with initial data
			const initialData = [...mockDelegationRequests];
			component.dataSource = new MatTableDataSource<DelegationRequest>(initialData);

			// Create a changes object without the records property
			const changes = {
				totalRecords: new SimpleChange(0, 20, true),
			};

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data remains unchanged
			expect(component.dataSource.data).toEqual(initialData);
		});
	});

	describe('onTableScroll', () => {
		it('should emit loadMoreData when conditions are met', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).toHaveBeenCalled();
		});

		it('should not emit loadMoreData when enableInfiniteScroll is false', () => {
			component.enableInfiniteScroll = false;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should not emit loadMoreData when dataLoading is true', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = true;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should not emit loadMoreData when hasMoreData is false', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = false;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should not emit loadMoreData when not at bottom', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 50, // Not at bottom
					scrollHeight: 200,
					clientHeight: 100,
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).not.toHaveBeenCalled();
		});

		it('should handle edge case when scrollHeight equals scrollTop + clientHeight', () => {
			component.enableInfiniteScroll = true;
			component.dataLoading = false;
			component.hasMoreData = true;
			spyOn(component.loadMoreData, 'emit');

			const mockEvent = {
				target: {
					scrollTop: 100,
					scrollHeight: 200,
					clientHeight: 100, // scrollTop + clientHeight = scrollHeight
				} as HTMLDivElement,
			} as unknown as Event;

			component.onTableScroll(mockEvent);

			expect(component.loadMoreData.emit).toHaveBeenCalled();
		});
	});

	describe('onSortChange', () => {
		it('should update currentSort and emit sortChange event', () => {
			spyOn(component.sortChange, 'emit');
			spyOn(component.pagination, 'emit');

			const sort: Sort = { active: 'hasDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.currentSort).toEqual(sort);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sort);
			expect(component.pagination.emit).toHaveBeenCalled();
		});
	});

	describe('emitPaginationWithSort', () => {
		it('should emit pagination event with current sort parameters when no event provided', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'hasDescription', direction: 'asc' };

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 0,
				pageSize: 10,
				length: 20,
				sortField: 'hasDescription',
				sortDirection: 'asc',
			});
		});

		it('should emit pagination event with provided event and current sort parameters', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'grossWeight', direction: 'desc' };

			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
			};

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				...pageEvent,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			});
		});
	});

	describe('trackByLoId', () => {
		it('should return the id of the given delegation request', () => {
			expect(component.trackByLoId(mockDelegationRequests[0])).toBe('1');
		});
	});

	describe('pagination', () => {
		it('should emit pagination event when page changes', () => {
			spyOn(component.pagination, 'emit');
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 10,
				length: 100,
			};

			component.pagination.emit(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
		});
	});

	describe('Component Properties and Constants', () => {
		it('should have correct displayedColumns', () => {
			expect(component.displayedColumns).toEqual([
				'hasDescription',
				'isRequestedFor',
				'isRequestedBy',
				'isRequestedAt',
				'requestStatus',
			]);
		});

		it('should have correct tablePageSizes', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});

		it('should initialize with empty currentSort', () => {
			expect(component.currentSort).toEqual({ active: '', direction: '' });
		});

		it('should initialize dataSource with records', () => {
			expect(component.dataSource).toBeInstanceOf(MatTableDataSource);
		});
	});

	describe('Input Properties', () => {
		it('should handle records input', () => {
			component.records = mockDelegationRequests;
			expect(component.records).toEqual(mockDelegationRequests);
		});

		it('should handle totalRecords input', () => {
			const testTotal = 100;
			component.totalRecords = testTotal;
			expect(component.totalRecords).toBe(testTotal);
		});

		it('should handle pageParams input', () => {
			const testPageParams = { pageNum: 2, pageSize: 50 };
			component.pageParams = testPageParams;
			expect(component.pageParams).toEqual(testPageParams);
		});

		it('should handle enableInfiniteScroll input', () => {
			component.enableInfiniteScroll = true;
			expect(component.enableInfiniteScroll).toBe(true);

			component.enableInfiniteScroll = false;
			expect(component.enableInfiniteScroll).toBe(false);
		});

		it('should handle hasMoreData input', () => {
			component.hasMoreData = false;
			expect(component.hasMoreData).toBe(false);

			component.hasMoreData = true;
			expect(component.hasMoreData).toBe(true);
		});
	});

	describe('Output Events', () => {
		it('should emit sortChange event', () => {
			spyOn(component.sortChange, 'emit');
			const testSort: Sort = { active: 'hasDescription', direction: 'asc' };

			component.sortChange.emit(testSort);

			expect(component.sortChange.emit).toHaveBeenCalledWith(testSort);
		});

		it('should emit loadMoreData event', () => {
			spyOn(component.loadMoreData, 'emit');

			component.loadMoreData.emit();

			expect(component.loadMoreData.emit).toHaveBeenCalled();
		});

		it('should emit pagination event with extended properties', () => {
			spyOn(component.pagination, 'emit');
			const testPageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 1,
				pageSize: 20,
				length: 100,
				sortField: 'hasDescription',
				sortDirection: 'asc',
			};

			component.pagination.emit(testPageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith(testPageEvent);
		});
	});

	describe('openDelegationRequestDetail', () => {
		it('should fetch delegation detail and open dialog with the result', () => {
			const record = { requestId: '123', isApprovedBy: 'user123' };
			const mockDelegationDetail = {
				id: '1',
				requestId: '123',
				isRequestedFor: ['org123'],
				isRequestedBy: 'user456',
				isRequestedAt: '2023-05-15',
				requestStatus: 'approved',
				hasDescription: 'Test delegation',
				hasPermission: ['permission1'],
				hasLogisticsObject: ['lo789'],
			};

			mockDelegationRequestService.getDelegationDetail.and.returnValue(of(mockDelegationDetail));

			component.openDelegationRequestDetail(record as DelegationRequest);

			expect(mockDelegationRequestService.getDelegationDetail).toHaveBeenCalledWith(record.requestId);
			expect(mockDialog.open).toHaveBeenCalledWith(DelegationRequestDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					record: mockDelegationDetail,
					isApprovedBy: record.isApprovedBy,
				},
			});
		});

		it('should handle different request IDs correctly', () => {
			const record1 = { requestId: 'req123', isApprovedBy: 'user123' };
			const record2 = { requestId: 'req456', isApprovedBy: 'user456' };
			const mockDetail1: DelegationRequest = {
				id: '1',
				requestId: 'req123',
				isRequestedFor: ['org1'],
				isRequestedBy: 'user1',
				isRequestedAt: '2023-01-01',
				requestStatus: 'pending',
				hasDescription: 'Test request 1',
				hasPermission: ['perm1'],
				hasLogisticsObject: ['lo1'],
			};
			const mockDetail2: DelegationRequest = {
				id: '2',
				requestId: 'req456',
				isRequestedFor: ['org2'],
				isRequestedBy: 'user2',
				isRequestedAt: '2023-02-02',
				requestStatus: 'approved',
				hasDescription: 'Test request 2',
				hasPermission: ['perm2'],
				hasLogisticsObject: ['lo2'],
			};

			mockDelegationRequestService.getDelegationDetail.and.returnValues(of(mockDetail1), of(mockDetail2));

			// First call
			component.openDelegationRequestDetail(record1 as DelegationRequest);
			expect(mockDelegationRequestService.getDelegationDetail).toHaveBeenCalledWith(record1.requestId);
			expect(mockDialog.open).toHaveBeenCalledWith(DelegationRequestDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					record: mockDetail1,
					isApprovedBy: record1.isApprovedBy,
				},
			});

			// Reset call counts
			mockDelegationRequestService.getDelegationDetail.calls.reset();
			mockDialog.open.calls.reset();

			// Second call
			component.openDelegationRequestDetail(record2 as DelegationRequest);
			expect(mockDelegationRequestService.getDelegationDetail).toHaveBeenCalledWith(record2.requestId);
			expect(mockDialog.open).toHaveBeenCalledWith(DelegationRequestDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					record: mockDetail2,
					isApprovedBy: record2.isApprovedBy,
				},
			});
		});
	});

	describe('createDelegationRequest', () => {
		it('should open dialog with correct configuration for creating a new delegation request', () => {
			component.createDelegationRequest();

			expect(mockDialog.open).toHaveBeenCalledWith(DelegationRequestDialogComponent, {
				width: '60vw',
				autoFocus: false,
				data: {
					icon: 'send',
					ok: 'common.dialog.delegation.request.send',
				},
			});
		});

		it('should call dialog.open exactly once', () => {
			component.createDelegationRequest();
			expect(mockDialog.open).toHaveBeenCalledTimes(1);

			// Reset the spy call count
			mockDialog.open.calls.reset();

			// Call the method again and verify it's called again
			component.createDelegationRequest();
			expect(mockDialog.open).toHaveBeenCalledTimes(1);
		});

		it('should maintain consistent dialog configuration across multiple calls', () => {
			// Define the expected dialog config type
			interface DialogConfig {
				width: string;
				autoFocus: boolean;
				data: {
					icon: string;
					ok: string;
				};
			}

			// First call
			component.createDelegationRequest();
			const firstCallArgs = mockDialog.open.calls.mostRecent().args;
			expect(firstCallArgs).toBeDefined();
			const firstComponent = firstCallArgs[0];
			const firstConfig = firstCallArgs[1] as DialogConfig;

			// Reset the spy
			mockDialog.open.calls.reset();

			// Second call
			component.createDelegationRequest();
			const secondCallArgs = mockDialog.open.calls.mostRecent().args;
			expect(secondCallArgs).toBeDefined();
			const secondComponent = secondCallArgs[0];
			const secondConfig = secondCallArgs[1] as DialogConfig;

			// Verify both calls used the same component and configuration
			expect(firstComponent).toBe(secondComponent);
			expect(firstConfig.width).toBe(secondConfig.width);
			expect(firstConfig.autoFocus).toBe(secondConfig.autoFocus);
			expect(firstConfig.data.icon).toBe(secondConfig.data.icon);
			expect(firstConfig.data.ok).toBe(secondConfig.data.ok);
		});
	});

	describe('Edge Cases and Error Handling', () => {
		it('should handle empty records array', () => {
			component.records = [];
			const changes = {
				records: new SimpleChange(mockDelegationRequests, [], false),
			};

			component.ngOnChanges(changes);

			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle sort with empty direction', () => {
			spyOn(component.sortChange, 'emit');
			spyOn(component.pagination, 'emit');
			const sort: Sort = { active: 'hasDescription', direction: '' };

			component.onSortChange(sort);

			expect(component.currentSort).toEqual(sort);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sort);
		});

		it('should handle trackByLoId with different logistics objects', () => {
			const request1: DelegationRequest = { ...mockDelegationRequests[0], id: 'UNIQUE1' };
			const request2: DelegationRequest = { ...mockDelegationRequests[0], id: 'UNIQUE2' };

			expect(component.trackByLoId(request1)).toBe('UNIQUE1');
			expect(component.trackByLoId(request2)).toBe('UNIQUE2');
			expect(component.trackByLoId(request1)).not.toBe(component.trackByLoId(request2));
		});
	});

	describe('Pagination Integration', () => {
		it('should handle emitPaginationWithSort with different page parameters', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'isRequestedFor', direction: 'desc' };
			component.pageParams = { pageNum: 3, pageSize: 25 };
			component.totalRecords = 150;

			const customPageEvent: PageEvent = {
				pageIndex: 4,
				pageSize: 100,
				length: 200,
			};

			(component as any).emitPaginationWithSort(customPageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 4,
				pageSize: 100,
				length: 200,
				sortField: 'isRequestedFor',
				sortDirection: 'desc',
			});
		});

		it('should use default page parameters when no event provided', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'isRequestedBy', direction: 'asc' };
			component.pageParams = { pageNum: 2, pageSize: 50 };
			component.totalRecords = 75;

			(component as any).emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 1, // pageNum - 1
				pageSize: 50,
				length: 75,
				sortField: 'isRequestedBy',
				sortDirection: 'asc',
			});
		});

		describe('Infinite scroll integration', () => {
			it('should handle scroll event when all conditions are false', () => {
				component.enableInfiniteScroll = false;
				component.dataLoading = true;
				component.hasMoreData = false;
				spyOn(component.loadMoreData, 'emit');

				const mockEvent = {
					target: {
						scrollTop: 100,
						scrollHeight: 200,
						clientHeight: 100,
					} as HTMLDivElement,
				} as unknown as Event;

				component.onTableScroll(mockEvent);

				expect(component.loadMoreData.emit).not.toHaveBeenCalled();
			});

			it('should handle scroll calculation edge cases', () => {
				component.enableInfiniteScroll = true;
				component.dataLoading = false;
				component.hasMoreData = true;
				spyOn(component.loadMoreData, 'emit');

				// Test when scrollHeight - (scrollTop + clientHeight) > 0 (not at bottom)
				const mockEvent = {
					target: {
						scrollTop: 99,
						scrollHeight: 200,
						clientHeight: 100, // 99 + 100 = 199, which is < 200
					} as HTMLDivElement,
				} as unknown as Event;

				component.onTableScroll(mockEvent);

				expect(component.loadMoreData.emit).not.toHaveBeenCalled();
			});
		});
	});
});
