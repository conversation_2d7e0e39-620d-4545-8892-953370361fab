import { Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationListObj } from 'src/app/modules/notification/models/notification.model';

@Component({
	selector: 'orll-notification',
	imports: [MatIconModule, TranslateModule],
	templateUrl: './notification.component.html',
	styleUrl: './notification.component.scss',
})
export class NotificationComponent {
	@Input() notifications!: NotificationListObj[];
}
